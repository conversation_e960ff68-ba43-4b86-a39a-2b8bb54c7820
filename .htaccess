# حراجنا - إعدادات Apache للإنتاج

# تفعيل إعادة الكتابة
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www إلى non-www (اختياري)
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# حماية الملفات الحساسة
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "config.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "database.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files ".env">
    Order Deny,Allow
    Deny from all
</Files>

# حماية مجلدات النظام
<Directory "config/">
    Order Deny,Allow
    Deny from all
</Directory>

<Directory "includes/">
    Order Deny,Allow
    Deny from all
</Directory>

# السماح بالوصول للصور والملفات الثابتة
<Directory "uploads/">
    Order Allow,Deny
    Allow from all
    <Files "*.php">
        Order Deny,Allow
        Deny from all
    </Files>
</Directory>

<Directory "assets/">
    Order Allow,Deny
    Allow from all
</Directory>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للمتصفح
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self';"
</IfModule>

# منع الوصول للملفات المخفية
<Files ".*">
    Order Deny,Allow
    Deny from all
</Files>

# منع عرض قائمة الملفات
Options -Indexes

# إعدادات PHP للإنتاج
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value display_errors Off
    php_value log_errors On
</IfModule>

# إعادة توجيه الصفحات الرئيسية
DirectoryIndex index.php index.html

# صفحات الأخطاء المخصصة
ErrorDocument 404 /pages/404.php
ErrorDocument 403 /pages/403.php
ErrorDocument 500 /pages/500.php

# منع الهجمات الشائعة
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|[|%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|[|%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|{||).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(%0|%A|%B|%C|%D|%E|%F|127\.0).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# تحسين الأداء
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/javascript .js
    AddType image/webp .webp
</IfModule>
