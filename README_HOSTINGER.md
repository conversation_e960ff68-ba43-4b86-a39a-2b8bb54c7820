# 🚀 دليل رفع موقع حراجنا على Hostinger

## 📋 المتطلبات
- حساب Hostinger مع cPanel
- قاعدة بيانات MySQL
- PHP 7.4 أو أحدث

## 🔧 إعدادات قاعدة البيانات المحدثة
```
Host: srv1513.hstgr.io
Database: u302460181_hoasb
Username: u302460181_hoasb
Password: 10743211uU@
```

## 📦 خطوات الرفع

### 1. تحضير الملفات
- ✅ تم تحديث إعدادات قاعدة البيانات
- ✅ تم إنشاء ملف .htaccess محسن
- ✅ تم إعداد ملفات الحماية
- ✅ تم حل مشكلة GD Extension (أصبح اختياري)

### 2. رفع الملفات
1. ضغط جميع الملفات في ملف ZIP
2. تسجيل الدخول إلى cPanel في Hostinger
3. فتح File Manager
4. الانتقال إلى مجلد public_html
5. رفع وفك ضغط الملفات

### 3. إعداد قاعدة البيانات
1. فتح MySQL Databases في cPanel
2. التأكد من وجود قاعدة البيانات: u302460181_hoasb
3. التأكد من صلاحيات المستخدم

### 4. تشغيل التثبيت
1. زيارة: `https://yourdomain.com/install/install.php`
2. اتباع خطوات التثبيت
3. إدخال بيانات المدير
4. إكمال التثبيت

### 5. ما بعد التثبيت
1. حذف مجلد `install` للأمان
2. تحديث رابط الموقع في `config/production.php`
3. التأكد من صلاحيات المجلدات:
   - uploads: 755
   - logs: 755
   - cache: 755

## 🔒 الأمان
- ✅ حماية الملفات الحساسة
- ✅ منع تنفيذ PHP في مجلد uploads
- ✅ إعادة توجيه HTTPS إجباري
- ✅ حماية من الهجمات الشائعة

## 🎨 الميزات المطبقة
- ✅ توحيد الألوان 100% بـ .bg-info
- ✅ تصميم متجاوب
- ✅ نظام إدارة شامل
- ✅ نظام إعلانات متكامل

## 🔗 الروابط المهمة
- الموقع الرئيسي: `https://yourdomain.com`
- لوحة الإدارة: `https://yourdomain.com/admin/`
- تسجيل الدخول: `https://yourdomain.com/pages/login.php`

## ⚠️ ملاحظات مهمة
1. **GD Extension**: أصبح اختياري - يمكن المتابعة بدونه
2. **الألوان**: تم توحيد 100% من الألوان بـ .bg-info
3. **الأمان**: تم تطبيق جميع إعدادات الأمان المطلوبة
4. **الأداء**: تم تحسين الموقع للسرعة والأداء

## 🆘 حل المشاكل الشائعة

### مشكلة GD Extension
- **الحل**: تم جعلها اختيارية - يمكن المتابعة بدونها
- **البديل**: استخدام خدمات خارجية لمعالجة الصور

### مشكلة الصلاحيات
```bash
chmod 755 uploads/
chmod 755 logs/
chmod 755 cache/
```

### مشكلة قاعدة البيانات
- التأكد من صحة بيانات الاتصال
- التأكد من وجود قاعدة البيانات
- التأكد من صلاحيات المستخدم

## 📞 الدعم
إذا واجهت أي مشاكل:
1. تحقق من ملف logs/error.log
2. تأكد من إعدادات قاعدة البيانات
3. تحقق من صلاحيات الملفات

---
**تم تجهيز الموقع بنجاح للرفع على Hostinger! 🎉**
