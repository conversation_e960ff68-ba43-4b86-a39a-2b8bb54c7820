<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

try {
    echo "<h2>إضافة جدول ماركات السيارات وتحديث جدول الإعلانات</h2>";
    
    // إنشاء جدول ماركات السيارات
    $sql_create_brands = "
    CREATE TABLE IF NOT EXISTS car_brands (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        arabic_name VARCHAR(100),
        logo_path VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        is_popular BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_popular (is_popular),
        INDEX idx_sort (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql_create_brands);
    echo "✅ تم إنشاء جدول ماركات السيارات<br>";
    
    // التحقق من وجود عمود car_brand_id في جدول ads
    $columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'car_brand_id'");
    
    if (empty($columns)) {
        // إضافة عمود car_brand_id إلى جدول ads
        $sql_add_column = "ALTER TABLE ads ADD COLUMN car_brand_id INT NULL AFTER category_id";
        $db->query($sql_add_column);
        echo "✅ تم إضافة عمود car_brand_id إلى جدول الإعلانات<br>";
        
        // إضافة المفتاح الخارجي
        $sql_add_fk = "ALTER TABLE ads ADD FOREIGN KEY (car_brand_id) REFERENCES car_brands(id) ON DELETE SET NULL";
        $db->query($sql_add_fk);
        echo "✅ تم إضافة المفتاح الخارجي<br>";
        
        // إضافة فهرس
        $sql_add_index = "ALTER TABLE ads ADD INDEX idx_car_brand (car_brand_id)";
        $db->query($sql_add_index);
        echo "✅ تم إضافة فهرس car_brand_id<br>";
    } else {
        echo "ℹ️ عمود car_brand_id موجود مسبقاً<br>";
    }
    
    // إدراج ماركات السيارات الشائعة
    $car_brands = [
        ['Toyota', 'تويوتا', 'toyota-logo.svg', 1, 1],
        ['Nissan', 'نيسان', 'nissan-logo.svg', 1, 1],
        ['Honda', 'هوندا', 'honda-logo.svg', 1, 1],
        ['Hyundai', 'هيونداي', 'hyundai-logo.svg', 1, 1],
        ['Kia', 'كيا', 'kia-logo.svg', 1, 1],
        ['Chevrolet', 'شيفروليه', 'chevrolet-logo.svg', 1, 1],
        ['Ford', 'فورد', 'ford-logo.svg', 1, 1],
        ['BMW', 'بي إم دبليو', 'bmw-logo.svg', 1, 1],
        ['Mercedes', 'مرسيدس', 'mercedes-logo.svg', 1, 1],
        ['Audi', 'أودي', 'audi-logo.svg', 1, 1],
        ['Lexus', 'لكزس', 'lexus-logo.svg', 1, 1],
        ['Mazda', 'مازدا', 'mazda-logo.svg', 1, 1],
        ['Volkswagen', 'فولكس واجن', 'volkswagen-logo.svg', 1, 0],
        ['Peugeot', 'بيجو', 'peugeot-logo.svg', 1, 0],
        ['Renault', 'رينو', 'renault-logo.svg', 1, 0],
        ['Mitsubishi', 'ميتسوبيشي', 'mitsubishi-logo.svg', 1, 0],
        ['Subaru', 'سوبارو', 'subaru-logo.svg', 1, 0],
        ['Suzuki', 'سوزوكي', 'suzuki-logo.svg', 1, 0],
        ['Infiniti', 'إنفينيتي', 'infiniti-logo.svg', 1, 0],
        ['Acura', 'أكورا', 'acura-logo.svg', 1, 0],
        ['Cadillac', 'كاديلاك', 'cadillac-logo.svg', 1, 0],
        ['Jeep', 'جيب', 'jeep-logo.svg', 1, 0],
        ['Land Rover', 'لاند روفر', 'landrover-logo.svg', 1, 0],
        ['Porsche', 'بورش', 'porsche-logo.svg', 1, 0],
        ['Jaguar', 'جاكوار', 'jaguar-logo.svg', 1, 0],
        ['Volvo', 'فولفو', 'volvo-logo.svg', 1, 0],
        ['Genesis', 'جينيسيس', 'genesis-logo.svg', 1, 0],
        ['Tesla', 'تيسلا', 'tesla-logo.svg', 1, 0],
        ['GMC', 'جي إم سي', 'gmc-logo.svg', 1, 0],
        ['Dodge', 'دودج', 'dodge-logo.svg', 1, 0]
    ];
    
    $brands_added = 0;
    foreach ($car_brands as $index => $brand) {
        // التحقق من عدم وجود الماركة مسبقاً
        $existing = $db->fetch("SELECT id FROM car_brands WHERE name = ?", [$brand[0]]);
        
        if (!$existing) {
            $db->query(
                "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order) VALUES (?, ?, ?, ?, ?, ?)",
                [$brand[0], $brand[1], $brand[2], $brand[3], $brand[4], $index + 1]
            );
            $brands_added++;
        }
    }
    
    echo "✅ تم إضافة {$brands_added} ماركة سيارة جديدة<br>";
    
    // التحقق من وجود أعمدة إضافية مطلوبة في جدول ads
    $sold_columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'is_sold'");
    if (empty($sold_columns)) {
        $sql_add_sold_columns = "
        ALTER TABLE ads 
        ADD COLUMN is_sold BOOLEAN DEFAULT FALSE AFTER status,
        ADD COLUMN sold_at TIMESTAMP NULL AFTER is_sold,
        ADD COLUMN hide_sold_at TIMESTAMP NULL AFTER sold_at";
        
        $db->query($sql_add_sold_columns);
        echo "✅ تم إضافة أعمدة الإعلانات المباعة<br>";
        
        // إضافة فهارس
        $db->query("ALTER TABLE ads ADD INDEX idx_is_sold (is_sold)");
        $db->query("ALTER TABLE ads ADD INDEX idx_sold_at (sold_at)");
        $db->query("ALTER TABLE ads ADD INDEX idx_hide_sold_at (hide_sold_at)");
        echo "✅ تم إضافة فهارس الإعلانات المباعة<br>";
    }
    
    echo "<br><h3>تم الانتهاء من جميع التحديثات بنجاح! ✅</h3>";
    echo '<br><a href="categories.php" class="btn btn-primary">العودة إلى لوحة التحكم</a>';
    echo '<br><a href="../pages/add-ad.php" class="btn btn-success mt-2">تجربة إضافة إعلان</a>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل الخطأ: " . $e->getTraceAsString();
}
?>
