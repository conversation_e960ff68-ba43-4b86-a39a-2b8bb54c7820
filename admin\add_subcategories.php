<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

try {
    // الفئات الفرعية المطلوبة
    $subcategories = [
        // عقارات
        ['شقق للبيع', 'عقارات', 'fas fa-building'],
        ['فلل للبيع', 'عقارات', 'fas fa-home'],
        ['أراضي للبيع', 'عقارات', 'fas fa-map'],
        ['شقق للإيجار', 'عقارات', 'fas fa-key'],
        ['فلل للإيجار', 'عقارات', 'fas fa-house-user'],
        ['محلات ومكاتب', 'عقارات', 'fas fa-store'],
        ['مستودعات', 'عقارات', 'fas fa-warehouse'],
        ['استراحات', 'عقارات', 'fas fa-campground'],
        
        // أجهزة إلكترونية
        ['جوالات وأجهزة ذكية', 'أجهزة إلكترونية', 'fas fa-mobile-alt'],
        ['أجهزة كمبيوتر ولابتوب', 'أجهزة إلكترونية', 'fas fa-laptop'],
        ['تلفزيونات وشاشات', 'أجهزة إلكترونية', 'fas fa-tv'],
        ['كاميرات وتصوير', 'أجهزة إلكترونية', 'fas fa-camera'],
        ['ألعاب فيديو', 'أجهزة إلكترونية', 'fas fa-gamepad'],
        ['أجهزة صوتية', 'أجهزة إلكترونية', 'fas fa-headphones'],
        ['إكسسوارات إلكترونية', 'أجهزة إلكترونية', 'fas fa-plug'],
        
        // وظائف
        ['وظائف حكومية', 'وظائف', 'fas fa-university'],
        ['وظائف خاصة', 'وظائف', 'fas fa-briefcase'],
        ['وظائف جزئية', 'وظائف', 'fas fa-clock'],
        ['أعمال حرة', 'وظائف', 'fas fa-user-tie'],
        ['تدريب ومتدربين', 'وظائف', 'fas fa-graduation-cap'],
        ['نقل كفالة', 'وظائف', 'fas fa-exchange-alt'],
        ['عمال ونقل', 'وظائف', 'fas fa-hard-hat'],
        
        // خدمات
        ['خدمات تنظيف', 'خدمات', 'fas fa-broom'],
        ['خدمات صيانة', 'خدمات', 'fas fa-tools'],
        ['خدمات نقل', 'خدمات', 'fas fa-truck'],
        ['خدمات تعليمية', 'خدمات', 'fas fa-chalkboard-teacher'],
        ['خدمات طبية', 'خدمات', 'fas fa-user-md'],
        ['خدمات قانونية', 'خدمات', 'fas fa-gavel'],
        ['خدمات تقنية', 'خدمات', 'fas fa-laptop-code'],
        
        // حيوانات وطيور
        ['قطط', 'حيوانات وطيور', 'fas fa-cat'],
        ['كلاب', 'حيوانات وطيور', 'fas fa-dog'],
        ['طيور', 'حيوانات وطيور', 'fas fa-dove'],
        ['أسماك', 'حيوانات وطيور', 'fas fa-fish'],
        ['خيول وإبل', 'حيوانات وطيور', 'fas fa-horse'],
        ['أغنام وماعز', 'حيوانات وطيور', 'fas fa-sheep'],
        ['إكسسوارات حيوانات', 'حيوانات وطيور', 'fas fa-bone'],
        
        // رياضة وترفيه
        ['معدات رياضية', 'رياضة وترفيه', 'fas fa-dumbbell'],
        ['دراجات هوائية', 'رياضة وترفيه', 'fas fa-bicycle'],
        ['ألعاب وترفيه', 'رياضة وترفيه', 'fas fa-puzzle-piece'],
        ['كتب ومجلات', 'رياضة وترفيه', 'fas fa-book'],
        ['آلات موسيقية', 'رياضة وترفيه', 'fas fa-music'],
        ['معدات تخييم', 'رياضة وترفيه', 'fas fa-campground'],
        ['معدات صيد', 'رياضة وترفيه', 'fas fa-fish']
    ];
    
    // إضافة الفئات الجديدة (البرمجة والتسويق)
    $new_main_categories = [
        ['البرمجة والتقنية', 'programming-tech', 'تطوير البرمجيات والخدمات التقنية', 'fas fa-code'],
        ['التسويق والإعلان', 'marketing-advertising', 'خدمات التسويق والإعلان والترويج', 'fas fa-bullhorn']
    ];
    
    // إضافة الفئات الرئيسية الجديدة
    foreach ($new_main_categories as $category) {
        $existing = $db->fetch("SELECT id FROM categories WHERE name = ? AND parent_id IS NULL", [$category[0]]);
        if (!$existing) {
            $db->query(
                "INSERT INTO categories (name, slug, description, icon, parent_id, is_active, created_at) VALUES (?, ?, ?, ?, NULL, 1, NOW())",
                [$category[0], $category[1], $category[2], $category[3]]
            );
            echo "تم إضافة الفئة الرئيسية: {$category[0]}<br>";
        }
    }
    
    // الفئات الفرعية للبرمجة والتقنية
    $programming_subcategories = [
        ['تطوير مواقع ويب', 'البرمجة والتقنية', 'fas fa-globe'],
        ['تطوير تطبيقات موبايل', 'البرمجة والتقنية', 'fas fa-mobile-alt'],
        ['تصميم جرافيك', 'البرمجة والتقنية', 'fas fa-paint-brush'],
        ['تصميم UI/UX', 'البرمجة والتقنية', 'fas fa-drafting-compass'],
        ['إدارة قواعد البيانات', 'البرمجة والتقنية', 'fas fa-database'],
        ['أمن المعلومات', 'البرمجة والتقنية', 'fas fa-shield-alt'],
        ['ذكاء اصطناعي', 'البرمجة والتقنية', 'fas fa-robot']
    ];
    
    // الفئات الفرعية للتسويق والإعلان
    $marketing_subcategories = [
        ['تسويق رقمي', 'التسويق والإعلان', 'fas fa-digital-tachograph'],
        ['إدارة وسائل التواصل', 'التسويق والإعلان', 'fas fa-share-alt'],
        ['كتابة المحتوى', 'التسويق والإعلان', 'fas fa-pen'],
        ['تحسين محركات البحث', 'التسويق والإعلان', 'fas fa-search'],
        ['إعلانات مدفوعة', 'التسويق والإعلان', 'fas fa-ad'],
        ['تصميم إعلانات', 'التسويق والإعلان', 'fas fa-palette'],
        ['استشارات تسويقية', 'التسويق والإعلان', 'fas fa-chart-line']
    ];
    
    // دمج جميع الفئات الفرعية
    $all_subcategories = array_merge($subcategories, $programming_subcategories, $marketing_subcategories);
    
    $subcategories_added = 0;
    
    foreach ($all_subcategories as $sub) {
        // جلب ID الفئة الرئيسية
        $parent = $db->fetch("SELECT id FROM categories WHERE name = ? AND parent_id IS NULL", [$sub[1]]);
        
        if ($parent) {
            // التحقق من عدم وجود الفئة الفرعية مسبقاً
            $existing_sub = $db->fetch("SELECT id FROM categories WHERE name = ? AND parent_id = ?", [$sub[0], $parent['id']]);
            
            if (!$existing_sub) {
                // إنشاء slug من اسم الفئة
                $slug = strtolower(str_replace([' ', 'و'], ['-', ''], $sub[0]));
                $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
                
                $db->query(
                    "INSERT INTO categories (name, slug, parent_id, icon, is_active, created_at) VALUES (?, ?, ?, ?, 1, NOW())",
                    [$sub[0], $slug, $parent['id'], $sub[2]]
                );
                $subcategories_added++;
                echo "تم إضافة الفئة الفرعية: {$sub[0]} تحت {$sub[1]}<br>";
            } else {
                echo "الفئة الفرعية موجودة مسبقاً: {$sub[0]}<br>";
            }
        } else {
            echo "لم يتم العثور على الفئة الرئيسية: {$sub[1]}<br>";
        }
    }
    
    echo "<br><strong>تم إضافة {$subcategories_added} فئة فرعية جديدة بنجاح!</strong><br>";
    echo '<br><a href="categories.php" class="btn btn-primary">العودة إلى إدارة الفئات</a>';
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
