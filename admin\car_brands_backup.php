<?php
/*
 * نسخة احتياطية من قسم ماركات السيارات
 * تم حذف هذا القسم من الصفحة الرئيسية بناءً على طلب المستخدم
 * يمكن استعادته لاحقاً عند الحاجة
 * 
 * تاريخ الحذف: <?= date('Y-m-d H:i:s') ?>
 * 
 * للاستعادة:
 * 1. أضف الكود HTML أدناه إلى pages/home.php
 * 2. أضف رابط CSS: <link href="../assets/css/car-brands.css" rel="stylesheet">
 * 3. أضف رابط JS: <script src="../assets/js/car-brands.js"></script>
 */

session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>نسخة احتياطية من قسم ماركات السيارات</h1>";
echo "<p class='alert alert-info'>تم حذف قسم ماركات السيارات من الصفحة الرئيسية. يمكن استعادته لاحقاً باستخدام الكود أدناه.</p>";

echo "<h2>1. الكود HTML (يُضاف إلى pages/home.php):</h2>";
echo "<textarea class='form-control' rows='25' style='font-family: monospace; font-size: 12px;'>";
echo htmlspecialchars('
    <!-- قسم السيارات الشهيرة -->
    <section class="car-brands-section">
        <div class="car-brands-container" id="car-brands-container">
            <h2 class="car-brands-title">
                <i class="fas fa-car me-2"></i>
                ابحث حسب ماركة السيارة
            </h2>
            <div class="car-brands-grid">
                <!-- مؤشر التحميل -->
                <div class="loading-spinner text-center" style="grid-column: 1 / -1;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل ماركات السيارات...</p>
                </div>
            </div>
            <button class="show-more-btn">
                <i class="fas fa-chevron-down me-2"></i>
                عرض المزيد
            </button>
        </div>
    </section>
');
echo "</textarea>";

echo "<h2>2. رابط CSS (يُضاف إلى head في pages/home.php):</h2>";
echo "<code>&lt;link href=\"../assets/css/car-brands.css\" rel=\"stylesheet\"&gt;</code>";

echo "<h2>3. رابط JavaScript (يُضاف قبل إغلاق body في pages/home.php):</h2>";
echo "<code>&lt;script src=\"../assets/js/car-brands.js\"&gt;&lt;/script&gt;</code>";

echo "<h2>4. حالة قاعدة البيانات الحالية:</h2>";

try {
    // فحص حالة قاعدة البيانات
    $brands_count = $db->fetch("SELECT COUNT(*) as count FROM car_brands WHERE is_active = 1")['count'];
    $popular_count = $db->fetch("SELECT COUNT(*) as count FROM car_brands WHERE is_active = 1 AND is_popular = 1")['count'];
    
    echo "<div class='alert alert-success'>";
    echo "✅ قاعدة البيانات سليمة<br>";
    echo "إجمالي الماركات النشطة: $brands_count<br>";
    echo "الماركات الشائعة: $popular_count<br>";
    echo "</div>";
    
    // عرض الماركات
    $brands = $db->fetchAll("SELECT name, arabic_name, logo_path, is_popular FROM car_brands WHERE is_active = 1 ORDER BY is_popular DESC, sort_order ASC");
    
    echo "<h3>الماركات المحفوظة:</h3>";
    echo "<div class='row'>";
    foreach ($brands as $brand) {
        $badge_class = $brand['is_popular'] ? 'bg-success' : 'bg-secondary';
        $popular_text = $brand['is_popular'] ? ' (شائع)' : '';
        
        echo "<div class='col-md-3 col-sm-4 col-6 mb-2'>";
        echo "<span class='badge $badge_class w-100'>";
        echo $brand['name'] . " - " . $brand['arabic_name'] . $popular_text;
        echo "</span>";
        echo "</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

echo "<h2>5. الملفات المحفوظة:</h2>";

$files_status = [
    'CSS' => '../assets/css/car-brands.css',
    'JavaScript' => '../assets/js/car-brands.js',
    'API' => '../api/get-car-brands.php'
];

foreach ($files_status as $type => $file_path) {
    $full_path = $root_path . '/' . ltrim($file_path, '../');
    $status = file_exists($full_path) ? '✅ موجود' : '❌ مفقود';
    echo "<p><strong>$type:</strong> $status - $file_path</p>";
}

echo "<h2>6. ملفات الشعارات:</h2>";

$logos_path = $root_path . '/assets/images/car-brands/';
if (is_dir($logos_path)) {
    $logo_files = scandir($logos_path);
    $svg_files = array_filter($logo_files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'svg';
    });
    
    echo "<div class='alert alert-info'>";
    echo "✅ مجلد الشعارات موجود ويحتوي على " . count($svg_files) . " ملف SVG";
    echo "</div>";
    
    echo "<details>";
    echo "<summary>عرض ملفات الشعارات</summary>";
    echo "<div class='row mt-2'>";
    foreach ($svg_files as $file) {
        echo "<div class='col-md-2 col-sm-3 col-4 mb-2'>";
        echo "<small class='badge bg-light text-dark w-100'>$file</small>";
        echo "</div>";
    }
    echo "</div>";
    echo "</details>";
} else {
    echo "<div class='alert alert-warning'>⚠️ مجلد الشعارات غير موجود</div>";
}

echo "<hr>";
echo "<h2>7. أدوات الاستعادة:</h2>";

echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<a href='organize_car_brands.php' class='btn btn-primary w-100 mb-2'>إعادة تنظيم الماركات</a>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<a href='test_car_brands_display.php' class='btn btn-info w-100 mb-2'>اختبار العرض</a>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<a href='manual_logo_upload.php' class='btn btn-success w-100 mb-2'>رفع شعارات جديدة</a>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-warning mt-4'>";
echo "<h5>ملاحظة مهمة:</h5>";
echo "<p>جميع البيانات والملفات محفوظة ولم يتم حذف أي شيء من الباك اند. تم حذف العرض فقط من الصفحة الرئيسية.</p>";
echo "<p>لاستعادة القسم، ما عليك سوى إضافة الكود HTML أعلاه إلى الصفحة الرئيسية مع روابط CSS و JavaScript.</p>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<a href='../pages/home.php' class='btn btn-secondary' target='_blank'>عرض الصفحة الرئيسية</a>";
echo "</div>";
?>
