<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';

echo "<h1>تشخيص مشاكل الموقع</h1>";

try {
    echo "<h2>1. فحص قاعدة البيانات</h2>";
    
    // فحص جدول ads
    echo "<h3>أ. جدول الإعلانات (ads):</h3>";
    $ads_columns = $db->fetchAll("SHOW COLUMNS FROM ads");
    $has_car_brand_id = false;
    $has_sold_columns = false;
    
    foreach ($ads_columns as $col) {
        if ($col['Field'] === 'car_brand_id') $has_car_brand_id = true;
        if ($col['Field'] === 'is_sold') $has_sold_columns = true;
    }
    
    echo $has_car_brand_id ? "✅ عمود car_brand_id موجود<br>" : "❌ عمود car_brand_id غير موجود<br>";
    echo $has_sold_columns ? "✅ أعمدة الإعلانات المباعة موجودة<br>" : "❌ أعمدة الإعلانات المباعة غير موجودة<br>";
    
    // فحص جدول car_brands
    echo "<h3>ب. جدول ماركات السيارات (car_brands):</h3>";
    try {
        $brands_count = $db->fetch("SELECT COUNT(*) as count FROM car_brands")['count'];
        echo "✅ جدول car_brands موجود ويحتوي على $brands_count ماركة<br>";
    } catch (Exception $e) {
        echo "❌ جدول car_brands غير موجود أو فارغ<br>";
    }
    
    // فحص الإعلانات الموجودة
    echo "<h3>ج. الإعلانات الموجودة:</h3>";
    $ads_count = $db->fetch("SELECT COUNT(*) as count FROM ads")['count'];
    $active_ads = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'];
    echo "إجمالي الإعلانات: $ads_count<br>";
    echo "الإعلانات النشطة: $active_ads<br>";
    
    if ($active_ads > 0) {
        $sample_ads = $db->fetchAll("SELECT id, title, status FROM ads WHERE status = 'active' LIMIT 3");
        echo "عينة من الإعلانات النشطة:<br>";
        foreach ($sample_ads as $ad) {
            echo "- {$ad['title']} (ID: {$ad['id']})<br>";
        }
    }
    
    echo "<h2>2. فحص ملفات الرفع</h2>";
    
    $upload_path = UPLOAD_PATH;
    $ads_upload_path = UPLOAD_PATH . 'ads/';
    
    echo "مسار الرفع الأساسي: $upload_path<br>";
    echo is_dir($upload_path) ? "✅ مجلد الرفع الأساسي موجود<br>" : "❌ مجلد الرفع الأساسي غير موجود<br>";
    echo is_writable($upload_path) ? "✅ مجلد الرفع الأساسي قابل للكتابة<br>" : "❌ مجلد الرفع الأساسي غير قابل للكتابة<br>";
    
    echo "مسار رفع الإعلانات: $ads_upload_path<br>";
    echo is_dir($ads_upload_path) ? "✅ مجلد رفع الإعلانات موجود<br>" : "❌ مجلد رفع الإعلانات غير موجود<br>";
    echo is_writable($ads_upload_path) ? "✅ مجلد رفع الإعلانات قابل للكتابة<br>" : "❌ مجلد رفع الإعلانات غير قابل للكتابة<br>";
    
    echo "<h2>3. اختبار البحث بماركة السيارة</h2>";
    
    // اختبار البحث بـ Toyota
    $brand = 'Toyota';
    $where_conditions = ["(a.status = 'active')"];
    $params = [];
    
    if ($has_car_brand_id) {
        $where_conditions[] = "(cb.name = ? OR cb.arabic_name = ? OR cb.name LIKE ? OR cb.arabic_name LIKE ? OR a.title LIKE ? OR a.description LIKE ?)";
        $params = [$brand, $brand, "%$brand%", "%$brand%", "%$brand%", "%$brand%"];
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT a.*, cb.name as brand_name, cb.arabic_name as brand_arabic
                FROM ads a
                LEFT JOIN car_brands cb ON a.car_brand_id = cb.id
                WHERE $where_clause
                LIMIT 5";
    } else {
        $where_conditions[] = "(a.title LIKE ? OR a.description LIKE ?)";
        $params = ["%$brand%", "%$brand%"];
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT a.*
                FROM ads a
                WHERE $where_clause
                LIMIT 5";
    }
    
    echo "استعلام البحث:<br><pre>" . htmlspecialchars($sql) . "</pre>";
    echo "المعاملات: " . implode(', ', $params) . "<br>";
    
    $search_results = $db->fetchAll($sql, $params);
    
    if ($search_results) {
        echo "✅ تم العثور على " . count($search_results) . " نتائج للبحث عن '$brand':<br>";
        foreach ($search_results as $result) {
            $brand_info = isset($result['brand_name']) ? " (ماركة: {$result['brand_name']})" : "";
            echo "- {$result['title']}$brand_info<br>";
        }
    } else {
        echo "❌ لم يتم العثور على نتائج للبحث عن '$brand'<br>";
    }
    
    echo "<h2>4. اختبار إضافة إعلان تجريبي</h2>";
    
    // محاولة إضافة إعلان تجريبي
    $test_title = "اختبار إضافة إعلان - " . date('Y-m-d H:i:s');
    
    try {
        if ($has_car_brand_id) {
            $sql = "INSERT INTO ads (user_id, category_id, car_brand_id, title, description, price, price_type, condition_type,
                                   city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                    VALUES (1, 1, 1, ?, 'وصف تجريبي للإعلان', 1000, 'fixed', 'new', 'الرياض', 'الرياض', 
                           '0501234567', '<EMAIL>', '0501234567', 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";
            $db->query($sql, [$test_title]);
        } else {
            $sql = "INSERT INTO ads (user_id, category_id, title, description, price, price_type, condition_type,
                                   city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                    VALUES (1, 1, ?, 'وصف تجريبي للإعلان', 1000, 'fixed', 'new', 'الرياض', 'الرياض', 
                           '0501234567', '<EMAIL>', '0501234567', 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";
            $db->query($sql, [$test_title]);
        }
        
        $test_ad_id = $db->lastInsertId();
        echo "✅ تم إضافة إعلان تجريبي بنجاح (ID: $test_ad_id)<br>";
        
        // حذف الإعلان التجريبي
        $db->query("DELETE FROM ads WHERE id = ?", [$test_ad_id]);
        echo "✅ تم حذف الإعلان التجريبي<br>";
        
    } catch (Exception $e) {
        echo "❌ فشل في إضافة الإعلان التجريبي: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>5. التوصيات</h2>";
    
    if (!$has_car_brand_id) {
        echo "🔧 <strong>يُنصح بتشغيل:</strong> <a href='add_car_brands_table.php'>إضافة جدول ماركات السيارات</a><br>";
    }
    
    if (!is_dir($ads_upload_path) || !is_writable($ads_upload_path)) {
        echo "🔧 <strong>يُنصح بإصلاح:</strong> صلاحيات مجلد الرفع<br>";
    }
    
    if ($active_ads == 0) {
        echo "🔧 <strong>يُنصح بإضافة:</strong> بعض الإعلانات التجريبية للاختبار<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في التشخيص: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}

echo '<br><br><h3>روابط مفيدة:</h3>';
echo '<a href="add_car_brands_table.php" class="btn btn-primary">إضافة جدول ماركات السيارات</a><br>';
echo '<a href="test_add_ad.php" class="btn btn-success">اختبار إضافة إعلان</a><br>';
echo '<a href="test_search.php" class="btn btn-info">اختبار البحث</a><br>';
echo '<a href="../pages/add-ad.php" class="btn btn-warning">صفحة إضافة إعلان</a><br>';
echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-secondary">البحث بـ Toyota</a><br>';
?>
