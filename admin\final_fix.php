<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>الإصلاح النهائي لجميع المشاكل</h1>";

try {
    $fixes_applied = 0;
    
    echo "<h2>1. إصلاح جدول ماركات السيارات</h2>";
    
    // إنشاء جدول car_brands
    $sql_create_brands = "
    CREATE TABLE IF NOT EXISTS car_brands (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        arabic_name VARCHAR(100),
        logo_path VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        is_popular BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_popular (is_popular),
        INDEX idx_sort (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql_create_brands);
    echo "✅ تم التأكد من وجود جدول car_brands<br>";
    
    // إضافة عمود car_brand_id إذا لم يكن موجوداً
    $columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'car_brand_id'");
    if (empty($columns)) {
        $sql_add_column = "ALTER TABLE ads ADD COLUMN car_brand_id INT NULL AFTER category_id";
        $db->query($sql_add_column);
        echo "✅ تم إضافة عمود car_brand_id<br>";
        $fixes_applied++;
        
        // إضافة المفتاح الخارجي
        try {
            $sql_add_fk = "ALTER TABLE ads ADD FOREIGN KEY (car_brand_id) REFERENCES car_brands(id) ON DELETE SET NULL";
            $db->query($sql_add_fk);
            echo "✅ تم إضافة المفتاح الخارجي<br>";
        } catch (Exception $e) {
            echo "⚠️ تحذير: لم يتم إضافة المفتاح الخارجي (قد يكون موجوداً)<br>";
        }
        
        // إضافة فهرس
        try {
            $sql_add_index = "ALTER TABLE ads ADD INDEX idx_car_brand (car_brand_id)";
            $db->query($sql_add_index);
            echo "✅ تم إضافة فهرس car_brand_id<br>";
        } catch (Exception $e) {
            echo "⚠️ تحذير: لم يتم إضافة الفهرس (قد يكون موجوداً)<br>";
        }
    }
    
    // إضافة أعمدة الإعلانات المباعة
    $sold_columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'is_sold'");
    if (empty($sold_columns)) {
        $sql_add_sold_columns = "
        ALTER TABLE ads 
        ADD COLUMN is_sold BOOLEAN DEFAULT FALSE AFTER status,
        ADD COLUMN sold_at TIMESTAMP NULL AFTER is_sold,
        ADD COLUMN hide_sold_at TIMESTAMP NULL AFTER sold_at";
        
        $db->query($sql_add_sold_columns);
        echo "✅ تم إضافة أعمدة الإعلانات المباعة<br>";
        $fixes_applied++;
        
        // إضافة فهارس
        $db->query("ALTER TABLE ads ADD INDEX idx_is_sold (is_sold)");
        $db->query("ALTER TABLE ads ADD INDEX idx_sold_at (sold_at)");
        $db->query("ALTER TABLE ads ADD INDEX idx_hide_sold_at (hide_sold_at)");
        echo "✅ تم إضافة فهارس الإعلانات المباعة<br>";
    }
    
    echo "<h2>2. إضافة ماركات السيارات</h2>";
    
    $car_brands = [
        ['Toyota', 'تويوتا', 'toyota-logo.svg', 1, 1, 1],
        ['Nissan', 'نيسان', 'nissan-logo.svg', 1, 1, 2],
        ['Honda', 'هوندا', 'honda-logo.svg', 1, 1, 3],
        ['Hyundai', 'هيونداي', 'hyundai-logo.svg', 1, 1, 4],
        ['Kia', 'كيا', 'kia-logo.svg', 1, 1, 5],
        ['Chevrolet', 'شيفروليه', 'chevrolet-logo.svg', 1, 1, 6],
        ['Ford', 'فورد', 'ford-logo.svg', 1, 1, 7],
        ['BMW', 'بي إم دبليو', 'bmw-logo.svg', 1, 1, 8],
        ['Mercedes', 'مرسيدس', 'mercedes-logo.svg', 1, 1, 9],
        ['Audi', 'أودي', 'audi-logo.svg', 1, 1, 10],
        ['Lexus', 'لكزس', 'lexus-logo.svg', 1, 1, 11],
        ['Mazda', 'مازدا', 'mazda-logo.svg', 1, 1, 12]
    ];
    
    $brands_added = 0;
    foreach ($car_brands as $brand) {
        $existing = $db->fetch("SELECT id FROM car_brands WHERE name = ?", [$brand[0]]);
        if (!$existing) {
            $db->query(
                "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order) VALUES (?, ?, ?, ?, ?, ?)",
                $brand
            );
            $brands_added++;
        }
    }
    
    echo "✅ تم إضافة {$brands_added} ماركة سيارة جديدة<br>";
    if ($brands_added > 0) $fixes_applied++;
    
    echo "<h2>3. إضافة إعلانات تجريبية</h2>";
    
    $ads_with_brands = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE car_brand_id IS NOT NULL AND status = 'active'")['count'];
    
    if ($ads_with_brands < 5) {
        $test_ads = [
            ['تويوتا كامري 2020 للبيع - حالة ممتازة', 'سيارة تويوتا كامري موديل 2020 في حالة ممتازة، استعمال شخصي، صيانة دورية منتظمة. السيارة نظيفة جداً ولا تحتاج أي إصلاحات. فحص شامل متاح.', 1, 85000],
            ['نيسان التيما 2019 نظيفة جداً', 'سيارة نيسان التيما موديل 2019، لون أبيض، فل كامل، استعمال خفيف، سيرفس منتظم في الوكالة. السيارة في حالة الوكالة تماماً.', 2, 72000],
            ['هوندا أكورد 2021 كالجديدة', 'سيارة هوندا أكورد موديل 2021، قطعت مسافة قليلة، تحت الضمان، جميع الخدمات في الوكالة. السيارة بحالة ممتازة جداً.', 3, 95000],
            ['هيونداي إلنترا 2020 اقتصادية', 'سيارة هيونداي إلنترا موديل 2020، اقتصادية في استهلاك الوقود، صيانة منتظمة، استعمال عائلي محافظ. السعر قابل للتفاوض.', 4, 65000],
            ['كيا سيراتو 2021 فل كامل', 'سيارة كيا سيراتو موديل 2021، فل كامل، جلد، شاشة، كاميرا خلفية، سنسر، تحت الضمان. السيارة بحالة الوكالة.', 5, 78000],
            ['شيفروليه كروز 2019 للبيع', 'سيارة شيفروليه كروز موديل 2019، استعمال خفيف، صيانة منتظمة، لون أسود، حالة جيدة جداً. السعر نهائي.', 6, 68000],
            ['فورد فوكس 2020 اقتصادية', 'سيارة فورد فوكس موديل 2020، اقتصادية في الوقود، صيانة دورية، استعمال شخصي، حالة ممتازة.', 7, 70000]
        ];
        
        $added_count = 0;
        foreach ($test_ads as $ad) {
            try {
                $sql = "INSERT INTO ads (user_id, category_id, car_brand_id, title, description, price, price_type, condition_type,
                                       city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                        VALUES (1, 1, ?, ?, ?, ?, 'negotiable', 'used', 'الرياض', 'الرياض', 
                               '0501234567', '<EMAIL>', '0501234567', 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";
                
                $db->query($sql, [$ad[2], $ad[0], $ad[1], $ad[3]]);
                $added_count++;
            } catch (Exception $e) {
                // تجاهل الأخطاء إذا كان الإعلان موجوداً
            }
        }
        
        echo "✅ تم إضافة $added_count إعلانات تجريبية<br>";
        if ($added_count > 0) $fixes_applied++;
    } else {
        echo "ℹ️ يوجد $ads_with_brands إعلانات بماركات سيارات<br>";
    }
    
    echo "<h2>4. اختبار النظام</h2>";
    
    // اختبار البحث
    $search_test = $db->fetchAll("
        SELECT a.*, cb.name as brand_name 
        FROM ads a 
        LEFT JOIN car_brands cb ON a.car_brand_id = cb.id 
        WHERE a.status = 'active' AND (cb.name = 'Toyota' OR a.title LIKE '%تويوتا%') 
        LIMIT 3
    ");
    
    if ($search_test) {
        echo "✅ البحث بماركة Toyota يعمل - تم العثور على " . count($search_test) . " نتائج<br>";
    } else {
        echo "❌ البحث بماركة Toyota لا يعمل<br>";
    }
    
    // اختبار صفحة الفئات
    try {
        $categories_test = $db->fetchAll("SELECT COUNT(*) as count FROM categories WHERE parent_id IS NULL LIMIT 1");
        echo "✅ صفحة الفئات يجب أن تعمل الآن<br>";
    } catch (Exception $e) {
        echo "❌ مشكلة في صفحة الفئات: " . $e->getMessage() . "<br>";
    }
    
    // اختبار صفحة إعلاناتي
    try {
        $my_ads_test = $db->fetchAll("SELECT COUNT(*) as count FROM ads LIMIT 1");
        echo "✅ صفحة إعلاناتي يجب أن تعمل الآن<br>";
    } catch (Exception $e) {
        echo "❌ مشكلة في صفحة إعلاناتي: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>✅ تم الانتهاء من جميع الإصلاحات</h2>";
    echo "<p><strong>تم تطبيق $fixes_applied إصلاحات بنجاح!</strong></p>";
    
    echo "<h3>اختبر الصفحات الآن:</h3>";
    echo '<a href="../pages/categories.php" class="btn btn-primary">صفحة الفئات</a><br>';
    echo '<a href="../pages/my-ads.php" class="btn btn-success">صفحة إعلاناتي</a><br>';
    echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-info">البحث بـ Toyota</a><br>';
    echo '<a href="../pages/search.php?brand=Nissan" class="btn btn-info">البحث بـ Nissan</a><br>';
    echo '<a href="../pages/add-ad.php" class="btn btn-warning">إضافة إعلان</a><br>';
    echo '<a href="../pages/home.php" class="btn btn-secondary">الصفحة الرئيسية</a><br>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}
?>
