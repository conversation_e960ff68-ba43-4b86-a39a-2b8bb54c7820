<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>إصلاح البحث بماركات السيارات</h1>";

try {
    echo "<h2>1. فحص الوضع الحالي</h2>";
    
    // فحص جدول car_brands
    $brands_exist = false;
    try {
        $brands_count = $db->fetch("SELECT COUNT(*) as count FROM car_brands")['count'];
        $brands_exist = true;
        echo "✅ جدول car_brands موجود ويحتوي على $brands_count ماركة<br>";
    } catch (Exception $e) {
        echo "❌ جدول car_brands غير موجود<br>";
    }
    
    // فحص عمود car_brand_id في جدول ads
    $car_brand_column_exists = false;
    try {
        $columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'car_brand_id'");
        $car_brand_column_exists = !empty($columns);
        echo $car_brand_column_exists ? "✅ عمود car_brand_id موجود في جدول ads<br>" : "❌ عمود car_brand_id غير موجود في جدول ads<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في فحص عمود car_brand_id<br>";
    }
    
    // فحص الإعلانات الموجودة
    $ads_count = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'];
    echo "عدد الإعلانات النشطة: $ads_count<br>";
    
    if (!$brands_exist || !$car_brand_column_exists) {
        echo "<h2>2. إنشاء الجداول والأعمدة المطلوبة</h2>";
        
        if (!$brands_exist) {
            // إنشاء جدول car_brands
            $sql_create_brands = "
            CREATE TABLE car_brands (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                arabic_name VARCHAR(100),
                logo_path VARCHAR(255),
                is_active BOOLEAN DEFAULT TRUE,
                is_popular BOOLEAN DEFAULT FALSE,
                sort_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_popular (is_popular),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->query($sql_create_brands);
            echo "✅ تم إنشاء جدول car_brands<br>";
            
            // إضافة ماركات السيارات
            $car_brands = [
                ['Toyota', 'تويوتا', 'toyota-logo.svg', 1, 1, 1],
                ['Nissan', 'نيسان', 'nissan-logo.svg', 1, 1, 2],
                ['Honda', 'هوندا', 'honda-logo.svg', 1, 1, 3],
                ['Hyundai', 'هيونداي', 'hyundai-logo.svg', 1, 1, 4],
                ['Kia', 'كيا', 'kia-logo.svg', 1, 1, 5],
                ['Chevrolet', 'شيفروليه', 'chevrolet-logo.svg', 1, 1, 6],
                ['Ford', 'فورد', 'ford-logo.svg', 1, 1, 7],
                ['BMW', 'بي إم دبليو', 'bmw-logo.svg', 1, 1, 8],
                ['Mercedes', 'مرسيدس', 'mercedes-logo.svg', 1, 1, 9],
                ['Audi', 'أودي', 'audi-logo.svg', 1, 1, 10]
            ];
            
            foreach ($car_brands as $brand) {
                $db->query(
                    "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order) VALUES (?, ?, ?, ?, ?, ?)",
                    $brand
                );
            }
            echo "✅ تم إضافة " . count($car_brands) . " ماركة سيارة<br>";
        }
        
        if (!$car_brand_column_exists) {
            // إضافة عمود car_brand_id
            $sql_add_column = "ALTER TABLE ads ADD COLUMN car_brand_id INT NULL AFTER category_id";
            $db->query($sql_add_column);
            echo "✅ تم إضافة عمود car_brand_id<br>";
            
            // إضافة المفتاح الخارجي
            $sql_add_fk = "ALTER TABLE ads ADD FOREIGN KEY (car_brand_id) REFERENCES car_brands(id) ON DELETE SET NULL";
            $db->query($sql_add_fk);
            echo "✅ تم إضافة المفتاح الخارجي<br>";
            
            // إضافة فهرس
            $sql_add_index = "ALTER TABLE ads ADD INDEX idx_car_brand (car_brand_id)";
            $db->query($sql_add_index);
            echo "✅ تم إضافة فهرس car_brand_id<br>";
        }
    }
    
    echo "<h2>3. إضافة إعلانات تجريبية بماركات سيارات</h2>";
    
    // التحقق من وجود إعلانات بماركات سيارات
    $ads_with_brands = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE car_brand_id IS NOT NULL")['count'];
    
    if ($ads_with_brands < 3) {
        // إضافة إعلانات تجريبية
        $test_ads = [
            [
                'title' => 'تويوتا كامري 2020 للبيع - حالة ممتازة',
                'description' => 'سيارة تويوتا كامري موديل 2020 في حالة ممتازة، استعمال شخصي، صيانة دورية منتظمة. السيارة نظيفة جداً ولا تحتاج أي إصلاحات. فحص شامل متاح.',
                'car_brand_id' => 1, // Toyota
                'price' => 85000
            ],
            [
                'title' => 'نيسان التيما 2019 نظيفة جداً',
                'description' => 'سيارة نيسان التيما موديل 2019، لون أبيض، فل كامل، استعمال خفيف، سيرفس منتظم في الوكالة. السيارة في حالة الوكالة تماماً.',
                'car_brand_id' => 2, // Nissan
                'price' => 72000
            ],
            [
                'title' => 'هوندا أكورد 2021 كالجديدة',
                'description' => 'سيارة هوندا أكورد موديل 2021، قطعت مسافة قليلة، تحت الضمان، جميع الخدمات في الوكالة. السيارة بحالة ممتازة جداً.',
                'car_brand_id' => 3, // Honda
                'price' => 95000
            ],
            [
                'title' => 'هيونداي إلنترا 2020 اقتصادية',
                'description' => 'سيارة هيونداي إلنترا موديل 2020، اقتصادية في استهلاك الوقود، صيانة منتظمة، استعمال عائلي محافظ. السعر قابل للتفاوض.',
                'car_brand_id' => 4, // Hyundai
                'price' => 65000
            ],
            [
                'title' => 'كيا سيراتو 2021 فل كامل',
                'description' => 'سيارة كيا سيراتو موديل 2021، فل كامل، جلد، شاشة، كاميرا خلفية، سنسر، تحت الضمان. السيارة بحالة الوكالة.',
                'car_brand_id' => 5, // Kia
                'price' => 78000
            ]
        ];
        
        $added_count = 0;
        foreach ($test_ads as $ad) {
            try {
                $sql = "INSERT INTO ads (user_id, category_id, car_brand_id, title, description, price, price_type, condition_type,
                                       city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                        VALUES (1, 1, ?, ?, ?, ?, 'negotiable', 'used', 'الرياض', 'الرياض', 
                               '0501234567', '<EMAIL>', '0501234567', 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";
                
                $db->query($sql, [$ad['car_brand_id'], $ad['title'], $ad['description'], $ad['price']]);
                $added_count++;
            } catch (Exception $e) {
                echo "خطأ في إضافة إعلان: " . $e->getMessage() . "<br>";
            }
        }
        
        echo "✅ تم إضافة $added_count إعلانات تجريبية بماركات سيارات<br>";
    } else {
        echo "ℹ️ يوجد $ads_with_brands إعلانات بماركات سيارات<br>";
    }
    
    echo "<h2>4. اختبار البحث</h2>";
    
    // اختبار البحث بـ Toyota
    $search_results = $db->fetchAll("
        SELECT a.*, cb.name as brand_name, cb.arabic_name as brand_arabic
        FROM ads a
        LEFT JOIN car_brands cb ON a.car_brand_id = cb.id
        WHERE a.status = 'active' AND (cb.name = 'Toyota' OR cb.arabic_name = 'تويوتا' OR a.title LIKE '%تويوتا%' OR a.title LIKE '%Toyota%')
        LIMIT 5
    ");
    
    if ($search_results) {
        echo "✅ تم العثور على " . count($search_results) . " نتائج للبحث عن Toyota:<br>";
        foreach ($search_results as $result) {
            echo "- {$result['title']} (ماركة: {$result['brand_name']})<br>";
        }
    } else {
        echo "❌ لم يتم العثور على نتائج للبحث عن Toyota<br>";
    }
    
    echo "<h2>✅ تم الانتهاء من الإصلاحات</h2>";
    echo "<p><strong>البحث بماركات السيارات يجب أن يعمل الآن!</strong></p>";
    
    echo "<h3>اختبر الآن:</h3>";
    echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-primary">البحث بـ Toyota</a><br>';
    echo '<a href="../pages/search.php?brand=Nissan" class="btn btn-primary">البحث بـ Nissan</a><br>';
    echo '<a href="../pages/search.php?brand=Honda" class="btn btn-primary">البحث بـ Honda</a><br>';
    echo '<a href="../pages/home.php" class="btn btn-info">الصفحة الرئيسية</a><br>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}
?>
