<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحية الإدمن
requireAdmin();

// جلب الإحصائيات
$stats = [
    'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE user_type = 'user'")['count'],
    'total_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads")['count'],
    'active_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'],
    'pending_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'pending'")['count'],
    'total_messages' => $db->fetch("SELECT COUNT(*) as count FROM messages")['count'],
    'total_reports' => $db->fetch("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'")['count'],
];

// جلب الإعلانات الأخيرة
$recent_ads = $db->fetchAll("
    SELECT a.*, u.username, c.name as category_name 
    FROM ads a 
    JOIN users u ON a.user_id = u.id 
    JOIN categories c ON a.category_id = c.id 
    ORDER BY a.created_at DESC 
    LIMIT 10
");

// جلب المستخدمين الجدد
$recent_users = $db->fetchAll("
    SELECT * FROM users 
    WHERE user_type = 'user' 
    ORDER BY created_at DESC 
    LIMIT 10
");

// جلب التقارير المعلقة
$pending_reports = $db->fetchAll("
    SELECT r.*, u.username as reporter_name, a.title as ad_title 
    FROM reports r 
    LEFT JOIN users u ON r.reporter_id = u.id 
    LEFT JOIN ads a ON r.reported_ad_id = a.id 
    WHERE r.status = 'pending' 
    ORDER BY r.created_at DESC 
    LIMIT 10
");

$page_title = 'لوحة التحكم الرئيسية';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">لوحة التحكم الرئيسية</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">تصدير</button>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي المستخدمين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?= number_format($stats['total_users']) ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        إجمالي الإعلانات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?= number_format($stats['total_ads']) ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        الإعلانات النشطة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?= number_format($stats['active_ads']) ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        إعلانات معلقة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?= number_format($stats['pending_ads']) ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- الإعلانات الأخيرة -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">الإعلانات الأخيرة</h6>
                            <a href="ads.php" class="btn btn-sm btn-primary">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>العنوان</th>
                                            <th>المستخدم</th>
                                            <th>الفئة</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_ads as $ad): ?>
                                        <tr>
                                            <td>
                                                <a href="ads.php?action=view&id=<?= $ad['id'] ?>">
                                                    <?= substr($ad['title'], 0, 30) ?>...
                                                </a>
                                            </td>
                                            <td><?= $ad['username'] ?></td>
                                            <td><?= $ad['category_name'] ?></td>
                                            <td>
                                                <span class="badge bg-<?= $ad['status'] === 'active' ? 'success' : ($ad['status'] === 'pending' ? 'warning' : 'secondary') ?>">
                                                    <?= $ad['status'] ?>
                                                </span>
                                            </td>
                                            <td><?= date('Y-m-d', strtotime($ad['created_at'])) ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المستخدمين الجدد -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-success">المستخدمين الجدد</h6>
                            <a href="users.php" class="btn btn-sm btn-success">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>المدينة</th>
                                            <th>التاريخ</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_users as $user): ?>
                                        <tr>
                                            <td>
                                                <a href="users.php?action=view&id=<?= $user['id'] ?>">
                                                    <?= $user['full_name'] ?>
                                                </a>
                                            </td>
                                            <td><?= $user['email'] ?></td>
                                            <td><?= $user['city'] ?></td>
                                            <td><?= date('Y-m-d', strtotime($user['created_at'])) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $user['is_verified'] ? 'success' : 'warning' ?>">
                                                    <?= $user['is_verified'] ? 'مفعل' : 'غير مفعل' ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقارير المعلقة -->
            <?php if (!empty($pending_reports)): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-danger">التقارير المعلقة</h6>
                            <a href="reports.php" class="btn btn-sm btn-danger">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>المبلغ</th>
                                            <th>الإعلان</th>
                                            <th>السبب</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pending_reports as $report): ?>
                                        <tr>
                                            <td><?= $report['reporter_name'] ?></td>
                                            <td>
                                                <?php if ($report['ad_title']): ?>
                                                    <a href="ads.php?action=view&id=<?= $report['reported_ad_id'] ?>">
                                                        <?= substr($report['ad_title'], 0, 30) ?>...
                                                    </a>
                                                <?php else: ?>
                                                    تقرير مستخدم
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $report['reason'] ?></td>
                                            <td><?= date('Y-m-d', strtotime($report['created_at'])) ?></td>
                                            <td>
                                                <a href="reports.php?action=view&id=<?= $report['id'] ?>" class="btn btn-sm btn-outline-primary">
                                                    مراجعة
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
