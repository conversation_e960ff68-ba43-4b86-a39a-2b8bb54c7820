<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

$message = '';
$error = '';

// معالجة رفع الملفات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['logo_files'])) {
    $target_logos_path = $root_path . '/assets/images/car-brands/';
    
    // إنشاء مجلد الشعارات إذا لم يكن موجوداً
    if (!is_dir($target_logos_path)) {
        mkdir($target_logos_path, 0755, true);
    }
    
    $uploaded_count = 0;
    $updated_count = 0;
    
    foreach ($_FILES['logo_files']['name'] as $key => $filename) {
        if ($_FILES['logo_files']['error'][$key] === UPLOAD_ERR_OK) {
            $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if ($file_extension === 'svg') {
                // استخراج اسم الماركة من اسم الملف
                $brand_name_from_file = pathinfo($filename, PATHINFO_FILENAME);
                $brand_name_clean = ucfirst(strtolower($brand_name_from_file));
                
                // البحث عن الماركة في قاعدة البيانات
                $brand = $db->fetch("SELECT * FROM car_brands WHERE LOWER(name) LIKE ?", ['%' . strtolower($brand_name_from_file) . '%']);
                
                if ($brand) {
                    $new_filename = strtolower($brand['name']) . '.svg';
                    $target_file = $target_logos_path . $new_filename;
                    
                    if (move_uploaded_file($_FILES['logo_files']['tmp_name'][$key], $target_file)) {
                        // تحديث قاعدة البيانات
                        $db->query("UPDATE car_brands SET logo_path = ? WHERE id = ?", [$new_filename, $brand['id']]);
                        $uploaded_count++;
                        $updated_count++;
                    }
                } else {
                    // إنشاء ماركة جديدة
                    $arabic_names = [
                        'toyota' => 'تويوتا',
                        'nissan' => 'نيسان',
                        'honda' => 'هوندا',
                        'hyundai' => 'هيونداي',
                        'kia' => 'كيا',
                        'chevrolet' => 'شيفروليه',
                        'ford' => 'فورد',
                        'bmw' => 'بي إم دبليو',
                        'mercedes' => 'مرسيدس',
                        'audi' => 'أودي',
                        'lexus' => 'لكزس',
                        'mazda' => 'مازدا',
                        'volkswagen' => 'فولكس واجن',
                        'peugeot' => 'بيجو',
                        'renault' => 'رينو'
                    ];
                    
                    $arabic_name = $arabic_names[strtolower($brand_name_from_file)] ?? $brand_name_clean;
                    $new_filename = strtolower($brand_name_clean) . '.svg';
                    $target_file = $target_logos_path . $new_filename;
                    
                    if (move_uploaded_file($_FILES['logo_files']['tmp_name'][$key], $target_file)) {
                        // إضافة ماركة جديدة
                        $db->query(
                            "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order) VALUES (?, ?, ?, 1, 0, 999)",
                            [$brand_name_clean, $arabic_name, $new_filename]
                        );
                        $uploaded_count++;
                    }
                }
            }
        }
    }
    
    if ($uploaded_count > 0) {
        $message = "تم رفع $uploaded_count شعار بنجاح وتحديث $updated_count ماركة";
    } else {
        $error = "لم يتم رفع أي ملفات. تأكد من أن الملفات بصيغة SVG";
    }
}

// جلب الماركات الحالية
$brands = $db->fetchAll("SELECT * FROM car_brands ORDER BY name");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع شعارات ماركات السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-upload"></i> رفع شعارات ماركات السيارات</h1>
        
        <?php if ($message): ?>
            <div class="alert alert-success"><?= $message ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cloud-upload-alt"></i> رفع ملفات SVG</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="logo_files" class="form-label">اختر ملفات SVG لشعارات السيارات</label>
                                <input type="file" class="form-control" id="logo_files" name="logo_files[]" 
                                       accept=".svg" multiple required>
                                <div class="form-text">
                                    يمكنك اختيار عدة ملفات SVG في نفس الوقت. 
                                    تأكد من أن أسماء الملفات تحتوي على اسم الماركة (مثل: toyota.svg, nissan.svg)
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> رفع الشعارات
                            </button>
                        </form>
                        
                        <hr>
                        
                        <h6>تعليمات:</h6>
                        <ul class="small">
                            <li>الملفات يجب أن تكون بصيغة SVG فقط</li>
                            <li>اسم الملف يجب أن يحتوي على اسم الماركة (مثل: toyota.svg)</li>
                            <li>سيتم تحديث الماركات الموجودة أو إنشاء ماركات جديدة</li>
                            <li>يمكنك رفع عدة ملفات في نفس الوقت</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> الماركات الحالية</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($brands as $brand): ?>
                            <div class="d-flex align-items-center mb-2 p-2 border rounded">
                                <div class="me-3">
                                    <?php if ($brand['logo_path']): ?>
                                        <img src="../assets/images/car-brands/<?= $brand['logo_path'] ?>" 
                                             alt="<?= $brand['name'] ?>" 
                                             style="width: 40px; height: 40px; object-fit: contain;">
                                    <?php else: ?>
                                        <div style="width: 40px; height: 40px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-car text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong><?= $brand['name'] ?></strong><br>
                                    <small class="text-muted"><?= $brand['arabic_name'] ?></small>
                                    <?php if (!$brand['logo_path']): ?>
                                        <br><small class="text-danger">لا يوجد شعار</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>معاينة الشعارات في الموقع</h3>
            <div class="row">
                <?php 
                $brands_with_logos = array_filter($brands, function($brand) {
                    return !empty($brand['logo_path']);
                });
                foreach (array_slice($brands_with_logos, 0, 12) as $brand): 
                ?>
                    <div class="col-md-2 col-sm-3 col-4 mb-3">
                        <div class="card text-center">
                            <div class="card-body p-2">
                                <img src="../assets/images/car-brands/<?= $brand['logo_path'] ?>" 
                                     alt="<?= $brand['name'] ?>" 
                                     style="width: 60px; height: 60px; object-fit: contain; margin-bottom: 10px;">
                                <div style="font-size: 12px;">
                                    <strong><?= $brand['name'] ?></strong><br>
                                    <small class="text-muted"><?= $brand['arabic_name'] ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="../pages/home.php" class="btn btn-success">
                <i class="fas fa-home"></i> عرض الصفحة الرئيسية
            </a>
            <a href="categories.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
