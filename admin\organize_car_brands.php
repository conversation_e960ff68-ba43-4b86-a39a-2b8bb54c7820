<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>إعادة ضبط وترتيب ماركات السيارات</h1>";

$target_logos_path = $root_path . '/assets/images/car-brands/';

try {
    echo "<h2>1. فحص الملفات المرفوعة</h2>";
    
    // جلب الملفات الموجودة
    if (is_dir($target_logos_path)) {
        $existing_files = scandir($target_logos_path);
        $svg_files = array_filter($existing_files, function($file) {
            return pathinfo($file, PATHINFO_EXTENSION) === 'svg';
        });
        
        echo "✅ تم العثور على " . count($svg_files) . " ملف SVG<br>";
        
        foreach ($svg_files as $file) {
            echo "- $file<br>";
        }
    } else {
        echo "❌ مجلد الشعارات غير موجود<br>";
        exit();
    }
    
    echo "<h2>2. مطابقة الملفات مع ماركات السيارات</h2>";
    
    // مطابقة أسماء الملفات مع أسماء الماركات
    $file_mapping = [
        'toyota-svgrepo-com.svg' => ['Toyota', 'تويوتا', 1, 1],
        'nissan-logo.svg' => ['Nissan', 'نيسان', 1, 2],
        'honda-svgrepo-com.svg' => ['Honda', 'هوندا', 1, 3],
        'hyundai-svgrepo-com.svg' => ['Hyundai', 'هيونداي', 1, 4],
        'kia-svgrepo-com.svg' => ['Kia', 'كيا', 1, 5],
        'chevrolet-svgrepo-com.svg' => ['Chevrolet', 'شيفروليه', 1, 6],
        'ford-svgrepo-com.svg' => ['Ford', 'فورد', 1, 7],
        'mercedes-benz-logo-svgrepo-com.svg' => ['Mercedes', 'مرسيدس', 1, 8],
        'audi-svgrepo-com.svg' => ['Audi', 'أودي', 1, 9],
        'lexus-logo-svgrepo-com.svg' => ['Lexus', 'لكزس', 1, 10],
        'mazda-svgrepo-com.svg' => ['Mazda', 'مازدا', 1, 11],
        'peugeot-alt-svgrepo-com.svg' => ['Peugeot', 'بيجو', 0, 12],
        'renault-alt-svgrepo-com.svg' => ['Renault', 'رينو', 0, 13],
        'mitsubishi-svgrepo-com.svg' => ['Mitsubishi', 'ميتسوبيشي', 0, 14],
        'subaru-svgrepo-com.svg' => ['Subaru', 'سوبارو', 0, 15],
        'suzuki-svgrepo-com.svg' => ['Suzuki', 'سوزوكي', 0, 16],
        'acura-svgrepo-com.svg' => ['Acura', 'أكورا', 0, 17],
        'cadillac-svgrepo-com.svg' => ['Cadillac', 'كاديلاك', 0, 18],
        'jeep-svgrepo-com.svg' => ['Jeep', 'جيب', 0, 19],
        'land-rover-svgrepo-com.svg' => ['Land Rover', 'لاند روفر', 0, 20],
        'porsche-svgrepo-com.svg' => ['Porsche', 'بورش', 0, 21],
        'genesis-svgrepo-com.svg' => ['Genesis', 'جينيسيس', 0, 22],
        'gmc-svgrepo-com.svg' => ['GMC', 'جي إم سي', 0, 23],
        'dodge-ram-logo-svgrepo-com.svg' => ['Dodge', 'دودج', 0, 24]
    ];
    
    echo "<h2>3. تحديث قاعدة البيانات</h2>";
    
    // مسح الماركات الموجودة
    $db->query("DELETE FROM car_brands");
    echo "✅ تم مسح الماركات القديمة<br>";
    
    $added_count = 0;
    
    foreach ($file_mapping as $filename => $brand_info) {
        if (in_array($filename, $svg_files)) {
            list($name, $arabic_name, $is_popular, $sort_order) = $brand_info;
            
            $db->query(
                "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order, created_at) VALUES (?, ?, ?, 1, ?, ?, NOW())",
                [$name, $arabic_name, $filename, $is_popular, $sort_order]
            );
            
            echo "✅ تم إضافة $name → $filename<br>";
            $added_count++;
        } else {
            echo "⚠️ الملف غير موجود: $filename<br>";
        }
    }
    
    // إضافة BMW إذا لم يكن موجوداً (قد يكون بأسماء مختلفة)
    $bmw_files = array_filter($svg_files, function($file) {
        return stripos($file, 'bmw') !== false;
    });
    
    if (!empty($bmw_files)) {
        $bmw_file = reset($bmw_files);
        $db->query(
            "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order, created_at) VALUES (?, ?, ?, 1, 1, 12, NOW())",
            ['BMW', 'بي إم دبليو', $bmw_file]
        );
        echo "✅ تم إضافة BMW → $bmw_file<br>";
        $added_count++;
    }
    
    // إضافة Volkswagen إذا كان موجوداً
    $vw_files = array_filter($svg_files, function($file) {
        return stripos($file, 'volkswagen') !== false || stripos($file, 'vw') !== false;
    });
    
    if (!empty($vw_files)) {
        $vw_file = reset($vw_files);
        $db->query(
            "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order, created_at) VALUES (?, ?, ?, 1, 0, 25, NOW())",
            ['Volkswagen', 'فولكس واجن', $vw_file]
        );
        echo "✅ تم إضافة Volkswagen → $vw_file<br>";
        $added_count++;
    }
    
    echo "<h2>4. إعادة ترتيب العرض</h2>";
    
    // تحديث ترتيب الماركات الشائعة
    $popular_brands = $db->fetchAll("SELECT * FROM car_brands WHERE is_popular = 1 ORDER BY sort_order");
    $other_brands = $db->fetchAll("SELECT * FROM car_brands WHERE is_popular = 0 ORDER BY sort_order");
    
    echo "✅ الماركات الشائعة: " . count($popular_brands) . " ماركة<br>";
    echo "✅ الماركات الأخرى: " . count($other_brands) . " ماركة<br>";
    
    echo "<h2>✅ تم الانتهاء من إعادة الضبط</h2>";
    echo "<p><strong>تم إضافة $added_count ماركة بنجاح!</strong></p>";
    
    echo "<h3>معاينة الماركات المرتبة:</h3>";
    
    // عرض الماركات الشائعة
    echo "<h4>الماركات الشائعة (ستظهر أولاً):</h4>";
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 15px; margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">';
    foreach ($popular_brands as $brand) {
        $logo_url = '../assets/images/car-brands/' . $brand['logo_path'];
        echo '<div style="border: 2px solid #28a745; padding: 15px; text-align: center; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';
        echo '<img src="' . $logo_url . '?v=' . time() . '" alt="' . $brand['name'] . '" style="width: 70px; height: 70px; object-fit: contain; margin-bottom: 10px;">';
        echo '<div style="font-size: 12px; font-weight: bold;">' . $brand['name'] . '</div>';
        echo '<div style="color: #666; font-size: 10px;">' . $brand['arabic_name'] . '</div>';
        echo '<div style="color: #28a745; font-size: 9px; margin-top: 5px;">⭐ شائع</div>';
        echo '</div>';
    }
    echo '</div>';
    
    // عرض الماركات الأخرى
    echo "<h4>الماركات الأخرى (ستظهر عند النقر على 'عرض المزيد'):</h4>";
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 15px; margin: 20px 0; padding: 20px; background: #f1f3f4; border-radius: 8px;">';
    foreach ($other_brands as $brand) {
        $logo_url = '../assets/images/car-brands/' . $brand['logo_path'];
        echo '<div style="border: 1px solid #ddd; padding: 15px; text-align: center; border-radius: 8px; background: white;">';
        echo '<img src="' . $logo_url . '?v=' . time() . '" alt="' . $brand['name'] . '" style="width: 70px; height: 70px; object-fit: contain; margin-bottom: 10px;">';
        echo '<div style="font-size: 12px; font-weight: bold;">' . $brand['name'] . '</div>';
        echo '<div style="color: #666; font-size: 10px;">' . $brand['arabic_name'] . '</div>';
        echo '</div>';
    }
    echo '</div>';
    
    echo "<h3>اختبر النتيجة:</h3>";
    echo '<a href="../pages/home.php" class="btn btn-primary btn-lg" target="_blank" style="margin: 10px;">🏠 الصفحة الرئيسية</a>';
    echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-success" target="_blank" style="margin: 10px;">🔍 البحث بـ Toyota</a>';
    echo '<a href="../pages/search.php?brand=BMW" class="btn btn-info" target="_blank" style="margin: 10px;">🔍 البحث بـ BMW</a>';
    echo '<a href="../pages/search.php?brand=Mercedes" class="btn btn-warning" target="_blank" style="margin: 10px;">🔍 البحث بـ Mercedes</a>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}
?>
