<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>إصلاح سريع لجميع المشاكل</h1>";

try {
    $fixes_applied = 0;
    
    echo "<h2>1. إنشاء جدول ماركات السيارات</h2>";
    
    // إنشاء جدول ماركات السيارات
    $sql_create_brands = "
    CREATE TABLE IF NOT EXISTS car_brands (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        arabic_name VARCHAR(100),
        logo_path VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        is_popular BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_popular (is_popular),
        INDEX idx_sort (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql_create_brands);
    echo "✅ تم إنشاء جدول ماركات السيارات<br>";
    $fixes_applied++;
    
    echo "<h2>2. تحديث جدول الإعلانات</h2>";
    
    // التحقق من وجود عمود car_brand_id
    $columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'car_brand_id'");
    
    if (empty($columns)) {
        // إضافة عمود car_brand_id
        $sql_add_column = "ALTER TABLE ads ADD COLUMN car_brand_id INT NULL AFTER category_id";
        $db->query($sql_add_column);
        echo "✅ تم إضافة عمود car_brand_id<br>";
        
        // إضافة المفتاح الخارجي
        $sql_add_fk = "ALTER TABLE ads ADD FOREIGN KEY (car_brand_id) REFERENCES car_brands(id) ON DELETE SET NULL";
        $db->query($sql_add_fk);
        echo "✅ تم إضافة المفتاح الخارجي<br>";
        
        // إضافة فهرس
        $sql_add_index = "ALTER TABLE ads ADD INDEX idx_car_brand (car_brand_id)";
        $db->query($sql_add_index);
        echo "✅ تم إضافة فهرس car_brand_id<br>";
        $fixes_applied++;
    } else {
        echo "ℹ️ عمود car_brand_id موجود مسبقاً<br>";
    }
    
    // التحقق من أعمدة الإعلانات المباعة
    $sold_columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'is_sold'");
    if (empty($sold_columns)) {
        $sql_add_sold_columns = "
        ALTER TABLE ads 
        ADD COLUMN is_sold BOOLEAN DEFAULT FALSE AFTER status,
        ADD COLUMN sold_at TIMESTAMP NULL AFTER is_sold,
        ADD COLUMN hide_sold_at TIMESTAMP NULL AFTER sold_at";
        
        $db->query($sql_add_sold_columns);
        echo "✅ تم إضافة أعمدة الإعلانات المباعة<br>";
        
        // إضافة فهارس
        $db->query("ALTER TABLE ads ADD INDEX idx_is_sold (is_sold)");
        $db->query("ALTER TABLE ads ADD INDEX idx_sold_at (sold_at)");
        $db->query("ALTER TABLE ads ADD INDEX idx_hide_sold_at (hide_sold_at)");
        echo "✅ تم إضافة فهارس الإعلانات المباعة<br>";
        $fixes_applied++;
    } else {
        echo "ℹ️ أعمدة الإعلانات المباعة موجودة مسبقاً<br>";
    }
    
    echo "<h2>3. إضافة ماركات السيارات</h2>";
    
    $car_brands = [
        ['Toyota', 'تويوتا', 'toyota-logo.svg', 1, 1, 1],
        ['Nissan', 'نيسان', 'nissan-logo.svg', 1, 1, 2],
        ['Honda', 'هوندا', 'honda-logo.svg', 1, 1, 3],
        ['Hyundai', 'هيونداي', 'hyundai-logo.svg', 1, 1, 4],
        ['Kia', 'كيا', 'kia-logo.svg', 1, 1, 5],
        ['Chevrolet', 'شيفروليه', 'chevrolet-logo.svg', 1, 1, 6],
        ['Ford', 'فورد', 'ford-logo.svg', 1, 1, 7],
        ['BMW', 'بي إم دبليو', 'bmw-logo.svg', 1, 1, 8],
        ['Mercedes', 'مرسيدس', 'mercedes-logo.svg', 1, 1, 9],
        ['Audi', 'أودي', 'audi-logo.svg', 1, 1, 10],
        ['Lexus', 'لكزس', 'lexus-logo.svg', 1, 1, 11],
        ['Mazda', 'مازدا', 'mazda-logo.svg', 1, 1, 12],
        ['Volkswagen', 'فولكس واجن', 'volkswagen-logo.svg', 1, 0, 13],
        ['Peugeot', 'بيجو', 'peugeot-logo.svg', 1, 0, 14],
        ['Renault', 'رينو', 'renault-logo.svg', 1, 0, 15]
    ];
    
    $brands_added = 0;
    foreach ($car_brands as $brand) {
        $existing = $db->fetch("SELECT id FROM car_brands WHERE name = ?", [$brand[0]]);
        
        if (!$existing) {
            $db->query(
                "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order) VALUES (?, ?, ?, ?, ?, ?)",
                $brand
            );
            $brands_added++;
        }
    }
    
    echo "✅ تم إضافة {$brands_added} ماركة سيارة جديدة<br>";
    if ($brands_added > 0) $fixes_applied++;
    
    echo "<h2>4. إنشاء مجلدات الرفع</h2>";
    
    $upload_dirs = [
        UPLOAD_PATH,
        UPLOAD_PATH . 'ads/',
        UPLOAD_PATH . 'categories/',
        UPLOAD_PATH . 'users/'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✅ تم إنشاء مجلد: $dir<br>";
                $fixes_applied++;
            } else {
                echo "❌ فشل في إنشاء مجلد: $dir<br>";
            }
        } else {
            echo "ℹ️ مجلد موجود: $dir<br>";
        }
    }
    
    echo "<h2>5. إضافة إعلانات تجريبية</h2>";
    
    // التحقق من وجود إعلانات
    $ads_count = $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'];
    
    if ($ads_count < 3) {
        // إضافة إعلانات تجريبية
        $test_ads = [
            [
                'title' => 'تويوتا كامري 2020 للبيع',
                'description' => 'سيارة تويوتا كامري موديل 2020 في حالة ممتازة، استعمال شخصي، صيانة دورية منتظمة. السيارة نظيفة جداً ولا تحتاج أي إصلاحات.',
                'car_brand_id' => 1,
                'price' => 85000
            ],
            [
                'title' => 'نيسان التيما 2019 نظيفة جداً',
                'description' => 'سيارة نيسان التيما موديل 2019، لون أبيض، فل كامل، استعمال خفيف، سيرفس منتظم في الوكالة.',
                'car_brand_id' => 2,
                'price' => 72000
            ],
            [
                'title' => 'هوندا أكورد 2021 كالجديدة',
                'description' => 'سيارة هوندا أكورد موديل 2021، قطعت مسافة قليلة، تحت الضمان، جميع الخدمات في الوكالة.',
                'car_brand_id' => 3,
                'price' => 95000
            ]
        ];
        
        foreach ($test_ads as $ad) {
            $sql = "INSERT INTO ads (user_id, category_id, car_brand_id, title, description, price, price_type, condition_type,
                                   city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                    VALUES (1, 1, ?, ?, ?, ?, 'negotiable', 'used', 'الرياض', 'الرياض', 
                           '0501234567', '<EMAIL>', '0501234567', 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";
            
            $db->query($sql, [$ad['car_brand_id'], $ad['title'], $ad['description'], $ad['price']]);
        }
        
        echo "✅ تم إضافة " . count($test_ads) . " إعلانات تجريبية<br>";
        $fixes_applied++;
    } else {
        echo "ℹ️ يوجد إعلانات كافية ($ads_count إعلان)<br>";
    }
    
    echo "<h2>✅ تم الانتهاء من الإصلاحات</h2>";
    echo "<p><strong>تم تطبيق $fixes_applied إصلاحات بنجاح!</strong></p>";
    
    echo "<h3>اختبر الآن:</h3>";
    echo '<a href="../pages/add-ad.php" class="btn btn-success">إضافة إعلان جديد</a><br>';
    echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-primary">البحث بـ Toyota</a><br>';
    echo '<a href="../pages/home.php" class="btn btn-info">الصفحة الرئيسية</a><br>';
    echo '<a href="diagnose_issues.php" class="btn btn-warning">تشخيص المشاكل</a><br>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}
?>
