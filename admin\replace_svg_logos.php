<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>استبدال شعارات ماركات السيارات</h1>";

// مسارات المجلدات
$user_svg_path = 'C:\\Users\\<USER>\\Pictures\\SVG';
$target_logos_path = $root_path . '/assets/images/car-brands/';

echo "<h2>1. فحص المجلدات</h2>";

// فحص مجلد المستخدم
if (is_dir($user_svg_path)) {
    $user_files = scandir($user_svg_path);
    $user_svg_files = array_filter($user_files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'svg';
    });
    
    echo "✅ مجلد SVG الخاص بك موجود ويحتوي على " . count($user_svg_files) . " ملف<br>";
    echo "<strong>الملفات المتاحة:</strong><br>";
    foreach ($user_svg_files as $file) {
        echo "- $file<br>";
    }
} else {
    echo "❌ مجلد SVG غير موجود في: $user_svg_path<br>";
    echo "<h3>حلول بديلة:</h3>";
    echo "1. <a href='manual_logo_upload.php'>رفع الملفات يدوياً</a><br>";
    echo "2. تحديد المسار الصحيح لمجلد SVG<br>";
    echo "3. نسخ الملفات إلى مجلد الموقع مباشرة<br>";
    exit();
}

// فحص مجلد الموقع
if (is_dir($target_logos_path)) {
    $current_files = scandir($target_logos_path);
    $current_svg_files = array_filter($current_files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'svg';
    });
    
    echo "✅ مجلد شعارات الموقع موجود ويحتوي على " . count($current_svg_files) . " ملف<br>";
} else {
    echo "❌ مجلد شعارات الموقع غير موجود<br>";
    exit();
}

echo "<h2>2. مطابقة واستبدال الملفات</h2>";

// قائمة مطابقة أسماء الملفات
$brand_mapping = [
    // الملف في مجلد المستخدم => الملف في الموقع
    'toyota' => 'toyota-logo.svg',
    'Toyota' => 'toyota-logo.svg',
    'TOYOTA' => 'toyota-logo.svg',
    'nissan' => 'nissan-logo.svg',
    'Nissan' => 'nissan-logo.svg',
    'NISSAN' => 'nissan-logo.svg',
    'honda' => 'honda-logo.svg',
    'Honda' => 'honda-logo.svg',
    'HONDA' => 'honda-logo.svg',
    'hyundai' => 'hyundai-logo.svg',
    'Hyundai' => 'hyundai-logo.svg',
    'HYUNDAI' => 'hyundai-logo.svg',
    'kia' => 'kia-logo.svg',
    'Kia' => 'kia-logo.svg',
    'KIA' => 'kia-logo.svg',
    'chevrolet' => 'chevrolet-logo.svg',
    'Chevrolet' => 'chevrolet-logo.svg',
    'CHEVROLET' => 'chevrolet-logo.svg',
    'chevy' => 'chevrolet-logo.svg',
    'Chevy' => 'chevrolet-logo.svg',
    'ford' => 'ford-logo.svg',
    'Ford' => 'ford-logo.svg',
    'FORD' => 'ford-logo.svg',
    'bmw' => 'bmw-logo.svg',
    'BMW' => 'bmw-logo.svg',
    'mercedes' => 'mercedes-logo.svg',
    'Mercedes' => 'mercedes-logo.svg',
    'MERCEDES' => 'mercedes-logo.svg',
    'mercedes-benz' => 'mercedes-logo.svg',
    'Mercedes-Benz' => 'mercedes-logo.svg',
    'audi' => 'audi-logo.svg',
    'Audi' => 'audi-logo.svg',
    'AUDI' => 'audi-logo.svg',
    'lexus' => 'lexus-logo.svg',
    'Lexus' => 'lexus-logo.svg',
    'LEXUS' => 'lexus-logo.svg',
    'mazda' => 'mazda-logo.svg',
    'Mazda' => 'mazda-logo.svg',
    'MAZDA' => 'mazda-logo.svg',
    'volkswagen' => 'volkswagen-logo.svg',
    'Volkswagen' => 'volkswagen-logo.svg',
    'VW' => 'volkswagen-logo.svg',
    'vw' => 'volkswagen-logo.svg',
    'peugeot' => 'peugeot-logo.svg',
    'Peugeot' => 'peugeot-logo.svg',
    'PEUGEOT' => 'peugeot-logo.svg',
    'renault' => 'renault-logo.svg',
    'Renault' => 'renault-logo.svg',
    'RENAULT' => 'renault-logo.svg',
    'mitsubishi' => 'mitsubishi-logo.svg',
    'Mitsubishi' => 'mitsubishi-logo.svg',
    'MITSUBISHI' => 'mitsubishi-logo.svg',
    'subaru' => 'subaru-logo.svg',
    'Subaru' => 'subaru-logo.svg',
    'SUBARU' => 'subaru-logo.svg',
    'suzuki' => 'suzuki-logo.svg',
    'Suzuki' => 'suzuki-logo.svg',
    'SUZUKI' => 'suzuki-logo.svg',
    'infiniti' => 'infiniti-logo.svg',
    'Infiniti' => 'infiniti-logo.svg',
    'INFINITI' => 'infiniti-logo.svg',
    'acura' => 'acura-logo.svg',
    'Acura' => 'acura-logo.svg',
    'ACURA' => 'acura-logo.svg',
    'cadillac' => 'cadillac-logo.svg',
    'Cadillac' => 'cadillac-logo.svg',
    'CADILLAC' => 'cadillac-logo.svg',
    'jeep' => 'jeep-logo.svg',
    'Jeep' => 'jeep-logo.svg',
    'JEEP' => 'jeep-logo.svg',
    'landrover' => 'landrover-logo.svg',
    'land-rover' => 'landrover-logo.svg',
    'Land-Rover' => 'landrover-logo.svg',
    'LANDROVER' => 'landrover-logo.svg',
    'porsche' => 'porsche-logo.svg',
    'Porsche' => 'porsche-logo.svg',
    'PORSCHE' => 'porsche-logo.svg',
    'jaguar' => 'jaguar-logo.svg',
    'Jaguar' => 'jaguar-logo.svg',
    'JAGUAR' => 'jaguar-logo.svg',
    'volvo' => 'volvo-logo.svg',
    'Volvo' => 'volvo-logo.svg',
    'VOLVO' => 'volvo-logo.svg',
    'genesis' => 'genesis-logo.svg',
    'Genesis' => 'genesis-logo.svg',
    'GENESIS' => 'genesis-logo.svg',
    'tesla' => 'tesla-logo.svg',
    'Tesla' => 'tesla-logo.svg',
    'TESLA' => 'tesla-logo.svg',
    'gmc' => 'gmc-logo.svg',
    'GMC' => 'gmc-logo.svg',
    'dodge' => 'dodge-logo.svg',
    'Dodge' => 'dodge-logo.svg',
    'DODGE' => 'dodge-logo.svg'
];

$replaced_count = 0;
$backup_created = false;

try {
    // إنشاء نسخة احتياطية
    if (!$backup_created) {
        $backup_dir = $target_logos_path . 'backup_' . date('Y-m-d_H-i-s') . '/';
        if (mkdir($backup_dir, 0755, true)) {
            foreach ($current_svg_files as $file) {
                copy($target_logos_path . $file, $backup_dir . $file);
            }
            echo "✅ تم إنشاء نسخة احتياطية في: $backup_dir<br>";
            $backup_created = true;
        }
    }
    
    // استبدال الملفات
    foreach ($user_svg_files as $user_file) {
        $user_file_name = pathinfo($user_file, PATHINFO_FILENAME);
        
        if (isset($brand_mapping[$user_file_name])) {
            $target_file = $brand_mapping[$user_file_name];
            $source_path = $user_svg_path . '/' . $user_file;
            $target_path = $target_logos_path . $target_file;
            
            if (copy($source_path, $target_path)) {
                echo "✅ تم استبدال $target_file بـ $user_file<br>";
                $replaced_count++;
            } else {
                echo "❌ فشل في استبدال $target_file<br>";
            }
        } else {
            echo "⚠️ لم يتم العثور على مطابقة للملف: $user_file<br>";
        }
    }
    
    echo "<h2>✅ تم الانتهاء من الاستبدال</h2>";
    echo "<p><strong>تم استبدال $replaced_count ملف شعار</strong></p>";
    
    if ($replaced_count > 0) {
        echo "<h3>معاينة الشعارات الجديدة:</h3>";
        
        // عرض الشعارات المحدثة
        $brands = $db->fetchAll("SELECT name, arabic_name, logo_path FROM car_brands WHERE logo_path IS NOT NULL ORDER BY is_popular DESC, name");
        
        echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;">';
        foreach (array_slice($brands, 0, 12) as $brand) {
            $logo_url = '../assets/images/car-brands/' . $brand['logo_path'];
            echo '<div style="border: 1px solid #ddd; padding: 15px; text-align: center; border-radius: 8px; background: white;">';
            echo '<img src="' . $logo_url . '?v=' . time() . '" alt="' . $brand['name'] . '" style="width: 80px; height: 80px; object-fit: contain; margin-bottom: 10px;">';
            echo '<div style="font-size: 14px;"><strong>' . $brand['name'] . '</strong></div>';
            echo '<div style="color: #666; font-size: 12px;">' . $brand['arabic_name'] . '</div>';
            echo '</div>';
        }
        echo '</div>';
        
        echo "<h3>اختبر الشعارات الجديدة:</h3>";
        echo '<a href="../pages/home.php" class="btn btn-primary" target="_blank">الصفحة الرئيسية - قسم ماركات السيارات</a><br>';
        echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-success" target="_blank">البحث بـ Toyota</a><br>';
        echo '<a href="../pages/search.php?brand=Nissan" class="btn btn-info" target="_blank">البحث بـ Nissan</a><br>';
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}

echo "<hr>";
echo "<h3>خيارات أخرى:</h3>";
echo '<a href="manual_logo_upload.php" class="btn btn-warning">رفع ملفات إضافية</a> ';
echo '<a href="update_car_brand_logos.php" class="btn btn-secondary">تحديث تلقائي</a> ';
echo '<a href="../pages/home.php" class="btn btn-primary">عرض النتيجة</a>';
?>
