<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

$message = '';
$error = '';

// معالجة طلب الاستعادة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['restore'])) {
    try {
        $home_file = $root_path . '/pages/home.php';
        $home_content = file_get_contents($home_file);
        
        // التحقق من عدم وجود القسم مسبقاً
        if (strpos($home_content, 'car-brands-section') !== false) {
            $error = 'قسم ماركات السيارات موجود مسبقاً في الصفحة الرئيسية!';
        } else {
            // إضافة رابط CSS
            $css_link = '    <link href="../assets/css/car-brands.css" rel="stylesheet">';
            $home_content = str_replace(
                '    <link href="../assets/css/simple-style.css" rel="stylesheet">',
                '    <link href="../assets/css/simple-style.css" rel="stylesheet">' . "\n" . $css_link,
                $home_content
            );
            
            // إضافة قسم HTML
            $car_brands_html = '
    <!-- قسم السيارات الشهيرة -->
    <section class="car-brands-section">
        <div class="car-brands-container" id="car-brands-container">
            <h2 class="car-brands-title">
                <i class="fas fa-car me-2"></i>
                ابحث حسب ماركة السيارة
            </h2>
            <div class="car-brands-grid">
                <!-- مؤشر التحميل -->
                <div class="loading-spinner text-center" style="grid-column: 1 / -1;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل ماركات السيارات...</p>
                </div>
            </div>
            <button class="show-more-btn">
                <i class="fas fa-chevron-down me-2"></i>
                عرض المزيد
            </button>
        </div>
    </section>

';
            
            // البحث عن مكان إدراج القسم (بعد شريط التنقل)
            $nav_end = '</nav>';
            $nav_pos = strpos($home_content, $nav_end);
            if ($nav_pos !== false) {
                $insert_pos = $nav_pos + strlen($nav_end);
                $home_content = substr_replace($home_content, $car_brands_html, $insert_pos, 0);
            }
            
            // إضافة رابط JavaScript
            $js_script = '    <script src="../assets/js/car-brands.js"></script>' . "\n";
            $home_content = str_replace(
                '<?php include \'includes/footer.php\'; ?>',
                $js_script . '<?php include \'includes/footer.php\'; ?>',
                $home_content
            );
            
            // حفظ الملف
            if (file_put_contents($home_file, $home_content)) {
                $message = 'تم استعادة قسم ماركات السيارات بنجاح!';
            } else {
                $error = 'فشل في حفظ الملف. تحقق من صلاحيات الكتابة.';
            }
        }
        
    } catch (Exception $e) {
        $error = 'خطأ: ' . $e->getMessage();
    }
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة قسم ماركات السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-undo"></i> استعادة قسم ماركات السيارات</h1>
        
        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= $message ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?= $error ?>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> معلومات الاستعادة</h5>
                    </div>
                    <div class="card-body">
                        <p>هذه الأداة ستقوم بإعادة إضافة قسم "ابحث حسب ماركة السيارة" إلى الصفحة الرئيسية.</p>
                        
                        <h6>ما سيتم إضافته:</h6>
                        <ul>
                            <li>رابط ملف CSS: <code>car-brands.css</code></li>
                            <li>قسم HTML لعرض ماركات السيارات</li>
                            <li>رابط ملف JavaScript: <code>car-brands.js</code></li>
                        </ul>
                        
                        <h6>التحقق من المتطلبات:</h6>
                        <?php
                        $requirements = [
                            'قاعدة البيانات' => false,
                            'ملف CSS' => false,
                            'ملف JavaScript' => false,
                            'ملف API' => false,
                            'مجلد الشعارات' => false
                        ];
                        
                        try {
                            // فحص قاعدة البيانات
                            $brands_count = $db->fetch("SELECT COUNT(*) as count FROM car_brands WHERE is_active = 1")['count'];
                            if ($brands_count > 0) {
                                $requirements['قاعدة البيانات'] = true;
                            }
                        } catch (Exception $e) {
                            // قاعدة البيانات غير متاحة
                        }
                        
                        // فحص الملفات
                        $files_to_check = [
                            'ملف CSS' => '/assets/css/car-brands.css',
                            'ملف JavaScript' => '/assets/js/car-brands.js',
                            'ملف API' => '/api/get-car-brands.php'
                        ];
                        
                        foreach ($files_to_check as $name => $path) {
                            if (file_exists($root_path . $path)) {
                                $requirements[$name] = true;
                            }
                        }
                        
                        // فحص مجلد الشعارات
                        $logos_path = $root_path . '/assets/images/car-brands/';
                        if (is_dir($logos_path)) {
                            $logo_files = scandir($logos_path);
                            $svg_count = count(array_filter($logo_files, function($file) {
                                return pathinfo($file, PATHINFO_EXTENSION) === 'svg';
                            }));
                            if ($svg_count > 0) {
                                $requirements['مجلد الشعارات'] = true;
                            }
                        }
                        
                        $all_ready = true;
                        foreach ($requirements as $name => $status) {
                            $icon = $status ? 'fas fa-check text-success' : 'fas fa-times text-danger';
                            $text = $status ? 'جاهز' : 'غير متاح';
                            echo "<div><i class='$icon'></i> $name: $text</div>";
                            if (!$status) $all_ready = false;
                        }
                        ?>
                        
                        <?php if ($all_ready): ?>
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-check-circle"></i> جميع المتطلبات متوفرة! يمكنك المتابعة بالاستعادة.
                            </div>
                            
                            <form method="post" class="mt-3">
                                <button type="submit" name="restore" class="btn btn-primary btn-lg">
                                    <i class="fas fa-undo"></i> استعادة قسم ماركات السيارات
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle"></i> بعض المتطلبات غير متوفرة. يرجى إعداد النظام أولاً.
                            </div>
                            
                            <div class="mt-3">
                                <a href="organize_car_brands.php" class="btn btn-info">
                                    <i class="fas fa-cog"></i> إعداد ماركات السيارات
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> أدوات أخرى</h5>
                    </div>
                    <div class="card-body">
                        <a href="car_brands_backup.php" class="btn btn-secondary w-100 mb-2">
                            <i class="fas fa-archive"></i> عرض النسخة الاحتياطية
                        </a>
                        <a href="test_car_brands_display.php" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-eye"></i> اختبار العرض
                        </a>
                        <a href="organize_car_brands.php" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-sort"></i> إعادة تنظيم الماركات
                        </a>
                        <a href="../pages/home.php" class="btn btn-success w-100" target="_blank">
                            <i class="fas fa-home"></i> عرض الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
