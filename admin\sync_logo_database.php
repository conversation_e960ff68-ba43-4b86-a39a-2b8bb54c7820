<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>مزامنة قاعدة البيانات مع ملفات الشعارات</h1>";

$target_logos_path = $root_path . '/assets/images/car-brands/';

try {
    echo "<h2>1. فحص الملفات الموجودة</h2>";
    
    // جلب الملفات الموجودة في المجلد
    if (is_dir($target_logos_path)) {
        $existing_files = scandir($target_logos_path);
        $svg_files = array_filter($existing_files, function($file) {
            return pathinfo($file, PATHINFO_EXTENSION) === 'svg' && $file !== 'default-car.svg';
        });
        
        echo "✅ تم العثور على " . count($svg_files) . " ملف شعار<br>";
        
        foreach ($svg_files as $file) {
            echo "- $file<br>";
        }
    } else {
        echo "❌ مجلد الشعارات غير موجود<br>";
        exit();
    }
    
    echo "<h2>2. تحديث قاعدة البيانات</h2>";
    
    // مطابقة أسماء الملفات مع أسماء الماركات
    $file_to_brand = [
        'toyota-logo.svg' => 'Toyota',
        'nissan-logo.svg' => 'Nissan',
        'honda-logo.svg' => 'Honda',
        'hyundai-logo.svg' => 'Hyundai',
        'kia-logo.svg' => 'Kia',
        'chevrolet-logo.svg' => 'Chevrolet',
        'ford-logo.svg' => 'Ford',
        'bmw-logo.svg' => 'BMW',
        'mercedes-logo.svg' => 'Mercedes',
        'audi-logo.svg' => 'Audi',
        'lexus-logo.svg' => 'Lexus',
        'mazda-logo.svg' => 'Mazda',
        'volkswagen-logo.svg' => 'Volkswagen',
        'peugeot-logo.svg' => 'Peugeot',
        'renault-logo.svg' => 'Renault',
        'mitsubishi-logo.svg' => 'Mitsubishi',
        'subaru-logo.svg' => 'Subaru',
        'suzuki-logo.svg' => 'Suzuki',
        'infiniti-logo.svg' => 'Infiniti',
        'acura-logo.svg' => 'Acura',
        'cadillac-logo.svg' => 'Cadillac',
        'jeep-logo.svg' => 'Jeep',
        'landrover-logo.svg' => 'Land Rover',
        'porsche-logo.svg' => 'Porsche',
        'jaguar-logo.svg' => 'Jaguar',
        'volvo-logo.svg' => 'Volvo',
        'genesis-logo.svg' => 'Genesis',
        'tesla-logo.svg' => 'Tesla',
        'gmc-logo.svg' => 'GMC',
        'dodge-logo.svg' => 'Dodge'
    ];
    
    $updated_count = 0;
    $created_count = 0;
    
    foreach ($svg_files as $file) {
        if (isset($file_to_brand[$file])) {
            $brand_name = $file_to_brand[$file];
            
            // البحث عن الماركة في قاعدة البيانات
            $existing_brand = $db->fetch("SELECT * FROM car_brands WHERE name = ?", [$brand_name]);
            
            if ($existing_brand) {
                // تحديث مسار الشعار
                $db->query("UPDATE car_brands SET logo_path = ? WHERE id = ?", [$file, $existing_brand['id']]);
                echo "✅ تم تحديث شعار $brand_name → $file<br>";
                $updated_count++;
            } else {
                // إنشاء ماركة جديدة
                $arabic_names = [
                    'Toyota' => 'تويوتا',
                    'Nissan' => 'نيسان',
                    'Honda' => 'هوندا',
                    'Hyundai' => 'هيونداي',
                    'Kia' => 'كيا',
                    'Chevrolet' => 'شيفروليه',
                    'Ford' => 'فورد',
                    'BMW' => 'بي إم دبليو',
                    'Mercedes' => 'مرسيدس',
                    'Audi' => 'أودي',
                    'Lexus' => 'لكزس',
                    'Mazda' => 'مازدا',
                    'Volkswagen' => 'فولكس واجن',
                    'Peugeot' => 'بيجو',
                    'Renault' => 'رينو',
                    'Mitsubishi' => 'ميتسوبيشي',
                    'Subaru' => 'سوبارو',
                    'Suzuki' => 'سوزوكي',
                    'Infiniti' => 'إنفينيتي',
                    'Acura' => 'أكورا',
                    'Cadillac' => 'كاديلاك',
                    'Jeep' => 'جيب',
                    'Land Rover' => 'لاند روفر',
                    'Porsche' => 'بورش',
                    'Jaguar' => 'جاكوار',
                    'Volvo' => 'فولفو',
                    'Genesis' => 'جينيسيس',
                    'Tesla' => 'تيسلا',
                    'GMC' => 'جي إم سي',
                    'Dodge' => 'دودج'
                ];
                
                $arabic_name = $arabic_names[$brand_name] ?? $brand_name;
                $is_popular = in_array($brand_name, ['Toyota', 'Nissan', 'Honda', 'Hyundai', 'Kia', 'Chevrolet', 'Ford', 'BMW', 'Mercedes', 'Audi', 'Lexus', 'Mazda']) ? 1 : 0;
                
                $db->query(
                    "INSERT INTO car_brands (name, arabic_name, logo_path, is_active, is_popular, sort_order) VALUES (?, ?, ?, 1, ?, ?)",
                    [$brand_name, $arabic_name, $file, $is_popular, $created_count + 100]
                );
                
                echo "✅ تم إنشاء ماركة جديدة: $brand_name → $file<br>";
                $created_count++;
            }
        } else {
            echo "⚠️ ملف غير معروف: $file<br>";
        }
    }
    
    echo "<h2>3. تنظيف قاعدة البيانات</h2>";
    
    // إزالة الماركات التي لا تحتوي على شعارات
    $brands_without_logos = $db->fetchAll("SELECT * FROM car_brands WHERE logo_path IS NULL OR logo_path = ''");
    
    foreach ($brands_without_logos as $brand) {
        // محاولة العثور على شعار مناسب
        $possible_files = [
            strtolower($brand['name']) . '-logo.svg',
            strtolower($brand['name']) . '.svg',
            str_replace(' ', '-', strtolower($brand['name'])) . '-logo.svg'
        ];
        
        $found_file = null;
        foreach ($possible_files as $possible_file) {
            if (in_array($possible_file, $svg_files)) {
                $found_file = $possible_file;
                break;
            }
        }
        
        if ($found_file) {
            $db->query("UPDATE car_brands SET logo_path = ? WHERE id = ?", [$found_file, $brand['id']]);
            echo "✅ تم ربط شعار للماركة {$brand['name']} → $found_file<br>";
        } else {
            echo "⚠️ لم يتم العثور على شعار للماركة: {$brand['name']}<br>";
        }
    }
    
    echo "<h2>✅ تم الانتهاء من المزامنة</h2>";
    echo "<p><strong>النتائج:</strong></p>";
    echo "- تم تحديث $updated_count ماركة موجودة<br>";
    echo "- تم إنشاء $created_count ماركة جديدة<br>";
    
    echo "<h3>معاينة الماركات المحدثة:</h3>";
    
    // عرض جميع الماركات مع الشعارات
    $all_brands = $db->fetchAll("SELECT * FROM car_brands WHERE logo_path IS NOT NULL ORDER BY is_popular DESC, name");
    
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 10px; margin: 20px 0;">';
    foreach ($all_brands as $brand) {
        $logo_url = '../assets/images/car-brands/' . $brand['logo_path'];
        echo '<div style="border: 1px solid #ddd; padding: 10px; text-align: center; border-radius: 8px; background: white;">';
        echo '<img src="' . $logo_url . '?v=' . time() . '" alt="' . $brand['name'] . '" style="width: 60px; height: 60px; object-fit: contain; margin-bottom: 8px;">';
        echo '<div style="font-size: 11px;"><strong>' . $brand['name'] . '</strong></div>';
        echo '<div style="color: #666; font-size: 10px;">' . $brand['arabic_name'] . '</div>';
        if ($brand['is_popular']) {
            echo '<div style="color: #28a745; font-size: 9px;">شائع</div>';
        }
        echo '</div>';
    }
    echo '</div>';
    
    echo "<h3>اختبر النتيجة:</h3>";
    echo '<a href="../pages/home.php" class="btn btn-primary" target="_blank">الصفحة الرئيسية</a> ';
    echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-success" target="_blank">البحث بـ Toyota</a> ';
    echo '<a href="../pages/search.php?brand=BMW" class="btn btn-info" target="_blank">البحث بـ BMW</a>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}
?>
