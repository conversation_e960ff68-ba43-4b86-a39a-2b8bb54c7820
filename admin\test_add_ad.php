<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';

echo "<h2>اختبار إضافة الإعلانات</h2>";

try {
    // فحص جدول ads
    echo "<h3>1. فحص جدول الإعلانات:</h3>";
    $columns = $db->fetchAll("SHOW COLUMNS FROM ads");
    echo "✅ أعمدة جدول ads:<br>";
    foreach ($columns as $col) {
        echo "- {$col['Field']} ({$col['Type']})<br>";
    }
    
    echo "<br><h3>2. فحص مجلد الرفع:</h3>";
    $upload_dir = UPLOAD_PATH . 'ads/';
    if (is_dir($upload_dir)) {
        if (is_writable($upload_dir)) {
            echo "✅ مجلد الرفع موجود وقابل للكتابة: $upload_dir<br>";
        } else {
            echo "❌ مجلد الرفع غير قابل للكتابة: $upload_dir<br>";
        }
    } else {
        echo "❌ مجلد الرفع غير موجود: $upload_dir<br>";
        if (mkdir($upload_dir, 0755, true)) {
            echo "✅ تم إنشاء مجلد الرفع<br>";
        } else {
            echo "❌ فشل في إنشاء مجلد الرفع<br>";
        }
    }
    
    echo "<br><h3>3. فحص الفئات:</h3>";
    $categories = $db->fetchAll("SELECT id, name FROM categories WHERE parent_id IS NULL LIMIT 5");
    if ($categories) {
        echo "✅ الفئات المتاحة:<br>";
        foreach ($categories as $cat) {
            echo "- {$cat['name']} (ID: {$cat['id']})<br>";
        }
    } else {
        echo "❌ لا توجد فئات<br>";
    }
    
    echo "<br><h3>4. فحص المستخدمين:</h3>";
    $users = $db->fetchAll("SELECT id, username FROM users LIMIT 3");
    if ($users) {
        echo "✅ المستخدمون المتاحون:<br>";
        foreach ($users as $user) {
            echo "- {$user['username']} (ID: {$user['id']})<br>";
        }
    } else {
        echo "❌ لا يوجد مستخدمون<br>";
    }
    
    echo "<br><h3>5. اختبار إدراج إعلان تجريبي:</h3>";
    
    // التحقق من وجود عمود car_brand_id
    $car_brand_columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'car_brand_id'");
    $has_car_brand_column = !empty($car_brand_columns);
    
    if ($has_car_brand_column) {
        echo "✅ عمود car_brand_id موجود<br>";
        
        // إدراج إعلان تجريبي مع ماركة السيارة
        $test_data = [
            'user_id' => $users[0]['id'] ?? 1,
            'category_id' => $categories[0]['id'] ?? 1,
            'car_brand_id' => 1, // Toyota
            'title' => 'سيارة تويوتا كامري 2020 للبيع - اختبار',
            'description' => 'سيارة تويوتا كامري موديل 2020 في حالة ممتازة، استعمال شخصي، صيانة دورية منتظمة. السيارة نظيفة جداً ولا تحتاج أي إصلاحات.',
            'price' => 85000,
            'price_type' => 'negotiable',
            'condition_type' => 'used',
            'city' => 'الرياض',
            'region' => 'الرياض',
            'contact_phone' => '0501234567',
            'contact_email' => '<EMAIL>',
            'whatsapp' => '0501234567'
        ];
        
        $sql = "INSERT INTO ads (user_id, category_id, car_brand_id, title, description, price, price_type, condition_type,
                               city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";
        
        $db->query($sql, [
            $test_data['user_id'],
            $test_data['category_id'],
            $test_data['car_brand_id'],
            $test_data['title'],
            $test_data['description'],
            $test_data['price'],
            $test_data['price_type'],
            $test_data['condition_type'],
            $test_data['city'],
            $test_data['region'],
            $test_data['contact_phone'],
            $test_data['contact_email'],
            $test_data['whatsapp']
        ]);
        
        $ad_id = $db->lastInsertId();
        echo "✅ تم إدراج إعلان تجريبي بنجاح (ID: $ad_id)<br>";
        
    } else {
        echo "⚠️ عمود car_brand_id غير موجود، سيتم الإدراج بدونه<br>";
        
        // إدراج إعلان تجريبي بدون ماركة السيارة
        $test_data = [
            'user_id' => $users[0]['id'] ?? 1,
            'category_id' => $categories[0]['id'] ?? 1,
            'title' => 'إعلان تجريبي للاختبار',
            'description' => 'هذا إعلان تجريبي لاختبار النظام. يحتوي على وصف مفصل للمنتج أو الخدمة المعروضة.',
            'price' => 1000,
            'price_type' => 'fixed',
            'condition_type' => 'new',
            'city' => 'الرياض',
            'region' => 'الرياض',
            'contact_phone' => '0501234567',
            'contact_email' => '<EMAIL>',
            'whatsapp' => '0501234567'
        ];
        
        $sql = "INSERT INTO ads (user_id, category_id, title, description, price, price_type, condition_type,
                               city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";
        
        $db->query($sql, [
            $test_data['user_id'],
            $test_data['category_id'],
            $test_data['title'],
            $test_data['description'],
            $test_data['price'],
            $test_data['price_type'],
            $test_data['condition_type'],
            $test_data['city'],
            $test_data['region'],
            $test_data['contact_phone'],
            $test_data['contact_email'],
            $test_data['whatsapp']
        ]);
        
        $ad_id = $db->lastInsertId();
        echo "✅ تم إدراج إعلان تجريبي بنجاح (ID: $ad_id)<br>";
    }
    
    echo "<br><h3>6. اختبار البحث:</h3>";
    $search_results = $db->fetchAll("SELECT id, title FROM ads WHERE title LIKE '%تويوتا%' OR title LIKE '%Toyota%' LIMIT 3");
    if ($search_results) {
        echo "✅ نتائج البحث عن تويوتا:<br>";
        foreach ($search_results as $result) {
            echo "- {$result['title']} (ID: {$result['id']})<br>";
        }
    } else {
        echo "❌ لا توجد نتائج للبحث عن تويوتا<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}

echo '<br><br><a href="../pages/add-ad.php">اختبار إضافة إعلان</a>';
echo '<br><a href="../pages/search.php?brand=Toyota">اختبار البحث بـ Toyota</a>';
echo '<br><a href="../pages/home.php">العودة للصفحة الرئيسية</a>';
?>
