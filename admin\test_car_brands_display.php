<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>اختبار عرض ماركات السيارات</h1>";

try {
    // جلب ماركات السيارات من قاعدة البيانات
    $brands = $db->fetchAll("
        SELECT id, name, arabic_name, logo_path, is_popular, sort_order 
        FROM car_brands 
        WHERE is_active = 1 
        ORDER BY is_popular DESC, sort_order ASC, name ASC
    ");
    
    $popular_brands = array_filter($brands, function($brand) {
        return $brand['is_popular'] == 1;
    });
    
    $other_brands = array_filter($brands, function($brand) {
        return $brand['is_popular'] == 0;
    });
    
    echo "<h2>إحصائيات:</h2>";
    echo "إجمالي الماركات: " . count($brands) . "<br>";
    echo "الماركات الشائعة: " . count($popular_brands) . "<br>";
    echo "الماركات الأخرى: " . count($other_brands) . "<br>";
    
    echo "<h2>معاينة العرض كما سيظهر في الموقع:</h2>";
    
    // تضمين CSS
    echo '<link href="../assets/css/car-brands.css" rel="stylesheet">';
    echo '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">';
    echo '<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">';
    
    echo '<div class="car-brands-section" style="margin: 20px 0;">';
    echo '<div class="car-brands-container">';
    echo '<h2 class="car-brands-title"><i class="fas fa-car"></i> ابحث حسب ماركة السيارة</h2>';
    
    echo '<div class="car-brands-grid" id="car-brands-grid">';
    
    // عرض الماركات الشائعة
    foreach ($popular_brands as $index => $brand) {
        $logo_path = '../assets/images/car-brands/' . $brand['logo_path'];
        echo '<a href="../pages/search.php?brand=' . urlencode($brand['name']) . '" class="car-brand-item" style="animation-delay: ' . ($index * 0.1) . 's;">';
        echo '<div class="car-brand-logo">';
        echo '<img src="' . $logo_path . '" alt="' . $brand['name'] . '" onerror="this.src=\'../assets/images/car-brands/default-car.svg\'">';
        echo '</div>';
        echo '<div class="car-brand-name">' . $brand['name'] . '</div>';
        echo '<div class="car-brand-arabic">' . $brand['arabic_name'] . '</div>';
        echo '</a>';
    }
    
    // عرض الماركات الأخرى (مخفية في البداية)
    foreach ($other_brands as $index => $brand) {
        $logo_path = '../assets/images/car-brands/' . $brand['logo_path'];
        echo '<a href="../pages/search.php?brand=' . urlencode($brand['name']) . '" class="car-brand-item hidden-brands" style="animation-delay: ' . (($index + count($popular_brands)) * 0.1) . 's;">';
        echo '<div class="car-brand-logo">';
        echo '<img src="' . $logo_path . '" alt="' . $brand['name'] . '" onerror="this.src=\'../assets/images/car-brands/default-car.svg\'">';
        echo '</div>';
        echo '<div class="car-brand-name">' . $brand['name'] . '</div>';
        echo '<div class="car-brand-arabic">' . $brand['arabic_name'] . '</div>';
        echo '</a>';
    }
    
    echo '</div>'; // car-brands-grid
    
    if (count($other_brands) > 0) {
        echo '<button class="show-more-btn" onclick="toggleMoreBrands()">';
        echo '<i class="fas fa-chevron-down"></i> عرض المزيد (' . count($other_brands) . ' ماركة إضافية)';
        echo '</button>';
    }
    
    echo '</div>'; // car-brands-container
    echo '</div>'; // car-brands-section
    
    // JavaScript للتفاعل
    echo '<script>';
    echo 'function toggleMoreBrands() {';
    echo '    const hiddenBrands = document.querySelectorAll(".hidden-brands");';
    echo '    const button = document.querySelector(".show-more-btn");';
    echo '    const isShowing = hiddenBrands[0].classList.contains("show");';
    echo '    ';
    echo '    hiddenBrands.forEach(brand => {';
    echo '        if (isShowing) {';
    echo '            brand.classList.remove("show");';
    echo '        } else {';
    echo '            brand.classList.add("show");';
    echo '        }';
    echo '    });';
    echo '    ';
    echo '    if (isShowing) {';
    echo '        button.innerHTML = \'<i class="fas fa-chevron-down"></i> عرض المزيد (' . count($other_brands) . ' ماركة إضافية)\';';
    echo '    } else {';
    echo '        button.innerHTML = \'<i class="fas fa-chevron-up"></i> إخفاء الماركات الإضافية\';';
    echo '    }';
    echo '}';
    echo '</script>';
    
    echo "<hr>";
    echo "<h2>اختبار الروابط:</h2>";
    
    // اختبار بعض الروابط
    $test_brands = array_slice($brands, 0, 5);
    foreach ($test_brands as $brand) {
        $search_url = '../pages/search.php?brand=' . urlencode($brand['name']);
        echo '<a href="' . $search_url . '" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">';
        echo 'البحث بـ ' . $brand['name'];
        echo '</a>';
    }
    
    echo "<hr>";
    echo "<h2>فحص الملفات:</h2>";
    
    $missing_files = [];
    $existing_files = [];
    
    foreach ($brands as $brand) {
        $file_path = $root_path . '/assets/images/car-brands/' . $brand['logo_path'];
        if (file_exists($file_path)) {
            $existing_files[] = $brand['logo_path'];
        } else {
            $missing_files[] = $brand['logo_path'];
        }
    }
    
    echo "<h4>الملفات الموجودة (" . count($existing_files) . "):</h4>";
    foreach ($existing_files as $file) {
        echo '<span class="badge bg-success me-1 mb-1">' . $file . '</span>';
    }
    
    if (!empty($missing_files)) {
        echo "<h4>الملفات المفقودة (" . count($missing_files) . "):</h4>";
        foreach ($missing_files as $file) {
            echo '<span class="badge bg-danger me-1 mb-1">' . $file . '</span>';
        }
    }
    
    echo "<hr>";
    echo "<h2>اختبار API:</h2>";
    
    // اختبار API
    $api_url = '../api/get-car-brands.php';
    echo '<div id="api-test">';
    echo '<button onclick="testAPI()" class="btn btn-info">اختبار API</button>';
    echo '<div id="api-result" class="mt-3"></div>';
    echo '</div>';
    
    echo '<script>';
    echo 'async function testAPI() {';
    echo '    const resultDiv = document.getElementById("api-result");';
    echo '    resultDiv.innerHTML = "جاري الاختبار...";';
    echo '    ';
    echo '    try {';
    echo '        const response = await fetch("' . $api_url . '");';
    echo '        const data = await response.json();';
    echo '        ';
    echo '        if (data.success) {';
    echo '            resultDiv.innerHTML = `';
    echo '                <div class="alert alert-success">✅ API يعمل بنجاح!</div>';
    echo '                <p><strong>عدد الماركات:</strong> ${data.count}</p>';
    echo '                <details>';
    echo '                    <summary>عرض البيانات</summary>';
    echo '                    <pre>${JSON.stringify(data, null, 2)}</pre>';
    echo '                </details>';
    echo '            `;';
    echo '        } else {';
    echo '            resultDiv.innerHTML = `<div class="alert alert-danger">❌ خطأ في API: ${data.message}</div>`;';
    echo '        }';
    echo '    } catch (error) {';
    echo '        resultDiv.innerHTML = `<div class="alert alert-danger">❌ خطأ في الاتصال: ${error.message}</div>`;';
    echo '    }';
    echo '}';
    echo '</script>';
    
    echo "<hr>";
    echo "<h2>الخطوات التالية:</h2>";
    echo '<div class="alert alert-info">';
    echo '<h5>للتأكد من أن كل شيء يعمل:</h5>';
    echo '<ol>';
    echo '<li>تحقق من أن جميع الشعارات تظهر بشكل صحيح أعلاه</li>';
    echo '<li>اختبر API بالنقر على زر "اختبار API"</li>';
    echo '<li>اختبر الروابط للتأكد من أن البحث يعمل</li>';
    echo '<li>زر الصفحة الرئيسية للموقع لرؤية النتيجة النهائية</li>';
    echo '</ol>';
    echo '</div>';
    
    echo '<div class="mt-4">';
    echo '<a href="../pages/home.php" class="btn btn-primary btn-lg" target="_blank">';
    echo '<i class="fas fa-home"></i> عرض الصفحة الرئيسية';
    echo '</a>';
    echo '</div>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}
?>
