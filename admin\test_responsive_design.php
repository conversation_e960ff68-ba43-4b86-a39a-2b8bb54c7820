<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم المتجاوب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .device-frame {
            border: 2px solid #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .device-header {
            background: #333;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        
        .device-content {
            height: 500px;
            overflow-y: auto;
            background: white;
        }
        
        .desktop-frame {
            width: 100%;
            max-width: 1200px;
        }
        
        .tablet-frame {
            width: 768px;
            max-width: 90%;
        }
        
        .mobile-frame {
            width: 375px;
            max-width: 90%;
        }
        
        .test-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <div class="test-controls">
        <h6>اختبار التصميم المتجاوب</h6>
        <div class="btn-group-vertical w-100">
            <button class="btn btn-sm btn-primary" onclick="showDevice('desktop')">
                <i class="fas fa-desktop"></i> سطح المكتب
            </button>
            <button class="btn btn-sm btn-info" onclick="showDevice('tablet')">
                <i class="fas fa-tablet-alt"></i> تابلت
            </button>
            <button class="btn btn-sm btn-success" onclick="showDevice('mobile')">
                <i class="fas fa-mobile-alt"></i> جوال
            </button>
            <button class="btn btn-sm btn-warning" onclick="showAll()">
                <i class="fas fa-th"></i> عرض الكل
            </button>
        </div>
        <hr>
        <a href="../pages/home.php" class="btn btn-sm btn-secondary w-100" target="_blank">
            <i class="fas fa-external-link-alt"></i> الصفحة الأصلية
        </a>
    </div>

    <div class="container-fluid">
        <h1 class="text-center my-4">اختبار التصميم المتجاوب للصفحة الرئيسية</h1>
        
        <!-- عرض سطح المكتب -->
        <div id="desktop-view" class="device-frame desktop-frame">
            <div class="device-header">
                <i class="fas fa-desktop"></i> سطح المكتب (1200px+)
            </div>
            <div class="device-content">
                <iframe src="../pages/home.php" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
        
        <!-- عرض التابلت -->
        <div id="tablet-view" class="device-frame tablet-frame" style="display: none;">
            <div class="device-header">
                <i class="fas fa-tablet-alt"></i> تابلت (768px)
            </div>
            <div class="device-content">
                <iframe src="../pages/home.php" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
        
        <!-- عرض الجوال -->
        <div id="mobile-view" class="device-frame mobile-frame" style="display: none;">
            <div class="device-header">
                <i class="fas fa-mobile-alt"></i> جوال (375px)
            </div>
            <div class="device-content">
                <iframe src="../pages/home.php" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
        
        <!-- معلومات التصميم -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> معلومات التصميم الجديد</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>التخطيط الجديد:</h6>
                                <ul>
                                    <li><strong>Hero Section:</strong> مصغر في الأعلى</li>
                                    <li><strong>الفئات الرئيسية:</strong> في الجانب الأيمن (مصغرة)</li>
                                    <li><strong>أحدث الإعلانات:</strong> في الوسط (أعلى)</li>
                                    <li><strong>البحث السريع:</strong> تحت الإعلانات</li>
                                    <li><strong>إحصائيات الموقع:</strong> في الأسفل</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>التحسينات المتجاوبة:</h6>
                                <ul>
                                    <li><strong>سطح المكتب:</strong> 3 أعمدة (فئات + محتوى)</li>
                                    <li><strong>تابلت:</strong> عمودين (فئات أصغر + محتوى)</li>
                                    <li><strong>جوال:</strong> عمود واحد (فئات أعلى)</li>
                                    <li><strong>البطاقات:</strong> متجاوبة حسب حجم الشاشة</li>
                                    <li><strong>النصوص:</strong> أحجام متكيفة</li>
                                </ul>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <h6>نقاط الكسر (Breakpoints):</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="alert alert-primary">
                                    <strong>XL:</strong> 1200px+<br>
                                    <small>سطح المكتب الكبير</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="alert alert-info">
                                    <strong>LG:</strong> 992px+<br>
                                    <small>سطح المكتب</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="alert alert-warning">
                                    <strong>MD:</strong> 768px+<br>
                                    <small>تابلت</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="alert alert-success">
                                    <strong>SM:</strong> 576px+<br>
                                    <small>جوال كبير</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبارات الأداء -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tachometer-alt"></i> اختبارات الأداء</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-mobile-alt fa-3x text-success mb-2"></i>
                                    <h6>متوافق مع الجوال</h6>
                                    <p class="text-muted">تصميم متجاوب 100%</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-rocket fa-3x text-primary mb-2"></i>
                                    <h6>سرعة التحميل</h6>
                                    <p class="text-muted">محسن للأداء</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-eye fa-3x text-info mb-2"></i>
                                    <h6>تجربة المستخدم</h6>
                                    <p class="text-muted">سهل الاستخدام</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showDevice(device) {
            // إخفاء جميع الأجهزة
            document.getElementById('desktop-view').style.display = 'none';
            document.getElementById('tablet-view').style.display = 'none';
            document.getElementById('mobile-view').style.display = 'none';
            
            // إظهار الجهاز المحدد
            document.getElementById(device + '-view').style.display = 'block';
        }
        
        function showAll() {
            document.getElementById('desktop-view').style.display = 'block';
            document.getElementById('tablet-view').style.display = 'block';
            document.getElementById('mobile-view').style.display = 'block';
        }
        
        // تحديث الإطارات كل 5 ثوان
        setInterval(function() {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(iframe => {
                // لا نحدث الإطار لتجنب إعادة التحميل المستمر
                // iframe.src = iframe.src;
            });
        }, 30000);
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
