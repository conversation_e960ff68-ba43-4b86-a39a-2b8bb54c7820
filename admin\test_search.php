<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';

echo "<h2>اختبار البحث بماركة السيارة</h2>";

try {
    // فحص جدول car_brands
    echo "<h3>1. فحص جدول ماركات السيارات:</h3>";
    $brands = $db->fetchAll("SELECT * FROM car_brands LIMIT 5");
    if ($brands) {
        echo "✅ جدول car_brands موجود ويحتوي على " . count($brands) . " ماركات<br>";
        foreach ($brands as $brand) {
            echo "- {$brand['name']} ({$brand['arabic_name']})<br>";
        }
    } else {
        echo "❌ جدول car_brands فارغ أو غير موجود<br>";
    }
    
    echo "<br><h3>2. فحص جدول الإعلانات:</h3>";
    $ads = $db->fetchAll("SELECT id, title, car_brand_id FROM ads LIMIT 5");
    if ($ads) {
        echo "✅ جدول ads يحتوي على " . count($ads) . " إعلانات<br>";
        foreach ($ads as $ad) {
            echo "- إعلان #{$ad['id']}: {$ad['title']} (ماركة: {$ad['car_brand_id']})<br>";
        }
    } else {
        echo "❌ جدول ads فارغ<br>";
    }
    
    echo "<br><h3>3. اختبار البحث بـ Toyota:</h3>";
    $brand = 'Toyota';
    $where_conditions = ["(a.status = 'active' OR (a.is_sold = 1 AND a.hide_sold_at > NOW()))"];
    $params = [];
    
    $where_conditions[] = "(cb.name = ? OR cb.arabic_name = ? OR cb.name LIKE ? OR cb.arabic_name LIKE ? OR a.title LIKE ? OR a.description LIKE ?)";
    $params[] = $brand;
    $params[] = $brand;
    $params[] = "%$brand%";
    $params[] = "%$brand%";
    $params[] = "%$brand%";
    $params[] = "%$brand%";
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "SELECT a.*, c.name as category_name, u.username, u.full_name, cb.name as brand_name, cb.arabic_name as brand_arabic,
                   (SELECT image_path FROM ad_images WHERE ad_id = a.id ORDER BY is_primary DESC, id ASC LIMIT 1) as main_image
            FROM ads a
            LEFT JOIN categories c ON a.category_id = c.id
            LEFT JOIN users u ON a.user_id = u.id
            LEFT JOIN car_brands cb ON a.car_brand_id = cb.id
            WHERE $where_clause
            ORDER BY a.created_at DESC
            LIMIT 10";
    
    echo "الاستعلام: <pre>" . htmlspecialchars($sql) . "</pre>";
    echo "المعاملات: <pre>" . print_r($params, true) . "</pre>";
    
    $results = $db->fetchAll($sql, $params);
    
    if ($results) {
        echo "✅ تم العثور على " . count($results) . " نتائج:<br>";
        foreach ($results as $result) {
            echo "- {$result['title']} (ماركة: {$result['brand_name']})<br>";
        }
    } else {
        echo "❌ لم يتم العثور على نتائج<br>";
    }
    
    echo "<br><h3>4. اختبار البحث العام:</h3>";
    $general_search = $db->fetchAll("SELECT a.*, cb.name as brand_name FROM ads a LEFT JOIN car_brands cb ON a.car_brand_id = cb.id WHERE a.status = 'active' LIMIT 5");
    
    if ($general_search) {
        echo "✅ إعلانات نشطة موجودة:<br>";
        foreach ($general_search as $ad) {
            echo "- {$ad['title']} (ماركة: {$ad['brand_name']})<br>";
        }
    } else {
        echo "❌ لا توجد إعلانات نشطة<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br>تفاصيل: " . $e->getTraceAsString();
}

echo '<br><br><a href="../pages/search.php?brand=Toyota">اختبار البحث بـ Toyota</a>';
echo '<br><a href="../pages/home.php">العودة للصفحة الرئيسية</a>';
?>
