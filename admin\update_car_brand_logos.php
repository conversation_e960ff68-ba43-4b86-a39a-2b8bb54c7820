<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

echo "<h1>تحديث شعارات ماركات السيارات</h1>";

// مسار مجلد SVG المصدر
$source_svg_path = 'C:\\Users\\<USER>\\Pictures\\SVG';
// مسار مجلد الشعارات في الموقع
$target_logos_path = $root_path . '/assets/images/car-brands/';

// إنشاء مجلد الشعارات إذا لم يكن موجوداً
if (!is_dir($target_logos_path)) {
    if (mkdir($target_logos_path, 0755, true)) {
        echo "✅ تم إنشاء مجلد الشعارات: $target_logos_path<br>";
    } else {
        echo "❌ فشل في إنشاء مجلد الشعارات<br>";
        exit();
    }
}

echo "<h2>1. فحص ملفات SVG المتاحة</h2>";

// قائمة ماركات السيارات مع أسماء الملفات المتوقعة
$car_brands_mapping = [
    'Toyota' => ['toyota.svg', 'Toyota.svg', 'TOYOTA.svg'],
    'Nissan' => ['nissan.svg', 'Nissan.svg', 'NISSAN.svg'],
    'Honda' => ['honda.svg', 'Honda.svg', 'HONDA.svg'],
    'Hyundai' => ['hyundai.svg', 'Hyundai.svg', 'HYUNDAI.svg'],
    'Kia' => ['kia.svg', 'Kia.svg', 'KIA.svg'],
    'Chevrolet' => ['chevrolet.svg', 'Chevrolet.svg', 'CHEVROLET.svg', 'chevy.svg'],
    'Ford' => ['ford.svg', 'Ford.svg', 'FORD.svg'],
    'BMW' => ['bmw.svg', 'BMW.svg'],
    'Mercedes' => ['mercedes.svg', 'Mercedes.svg', 'MERCEDES.svg', 'mercedes-benz.svg'],
    'Audi' => ['audi.svg', 'Audi.svg', 'AUDI.svg'],
    'Lexus' => ['lexus.svg', 'Lexus.svg', 'LEXUS.svg'],
    'Mazda' => ['mazda.svg', 'Mazda.svg', 'MAZDA.svg'],
    'Volkswagen' => ['volkswagen.svg', 'Volkswagen.svg', 'VW.svg', 'vw.svg'],
    'Peugeot' => ['peugeot.svg', 'Peugeot.svg', 'PEUGEOT.svg'],
    'Renault' => ['renault.svg', 'Renault.svg', 'RENAULT.svg'],
    'Mitsubishi' => ['mitsubishi.svg', 'Mitsubishi.svg', 'MITSUBISHI.svg'],
    'Subaru' => ['subaru.svg', 'Subaru.svg', 'SUBARU.svg'],
    'Suzuki' => ['suzuki.svg', 'Suzuki.svg', 'SUZUKI.svg'],
    'Infiniti' => ['infiniti.svg', 'Infiniti.svg', 'INFINITI.svg'],
    'Acura' => ['acura.svg', 'Acura.svg', 'ACURA.svg'],
    'Cadillac' => ['cadillac.svg', 'Cadillac.svg', 'CADILLAC.svg'],
    'Jeep' => ['jeep.svg', 'Jeep.svg', 'JEEP.svg'],
    'Land Rover' => ['landrover.svg', 'land-rover.svg', 'Land-Rover.svg', 'LANDROVER.svg'],
    'Porsche' => ['porsche.svg', 'Porsche.svg', 'PORSCHE.svg'],
    'Jaguar' => ['jaguar.svg', 'Jaguar.svg', 'JAGUAR.svg'],
    'Volvo' => ['volvo.svg', 'Volvo.svg', 'VOLVO.svg'],
    'Genesis' => ['genesis.svg', 'Genesis.svg', 'GENESIS.svg'],
    'Tesla' => ['tesla.svg', 'Tesla.svg', 'TESLA.svg'],
    'GMC' => ['gmc.svg', 'GMC.svg'],
    'Dodge' => ['dodge.svg', 'Dodge.svg', 'DODGE.svg']
];

// فحص الملفات المتاحة
if (is_dir($source_svg_path)) {
    $available_files = scandir($source_svg_path);
    $svg_files = array_filter($available_files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'svg';
    });
    
    echo "✅ تم العثور على " . count($svg_files) . " ملف SVG في المجلد المصدر<br>";
    echo "<strong>الملفات المتاحة:</strong><br>";
    foreach ($svg_files as $file) {
        echo "- $file<br>";
    }
} else {
    echo "❌ مجلد SVG غير موجود: $source_svg_path<br>";
    echo "<h3>تعليمات:</h3>";
    echo "1. تأكد من وجود مجلد SVG في المسار المحدد<br>";
    echo "2. ضع ملفات SVG لماركات السيارات في هذا المجلد<br>";
    echo "3. تأكد من أن أسماء الملفات تطابق أسماء الماركات (مثل: toyota.svg, nissan.svg)<br>";
    exit();
}

echo "<h2>2. نسخ وتحديث الشعارات</h2>";

$updated_brands = 0;
$copied_files = 0;

try {
    foreach ($car_brands_mapping as $brand_name => $possible_files) {
        $found_file = null;
        
        // البحث عن الملف المناسب
        foreach ($possible_files as $filename) {
            if (in_array($filename, $svg_files)) {
                $found_file = $filename;
                break;
            }
        }
        
        if ($found_file) {
            $source_file = $source_svg_path . '/' . $found_file;
            $target_filename = strtolower($brand_name) . '.svg';
            $target_file = $target_logos_path . $target_filename;
            
            // نسخ الملف
            if (copy($source_file, $target_file)) {
                echo "✅ تم نسخ شعار $brand_name: $found_file → $target_filename<br>";
                $copied_files++;
                
                // تحديث قاعدة البيانات
                $logo_path = $target_filename;
                $updated = $db->query(
                    "UPDATE car_brands SET logo_path = ? WHERE name = ?",
                    [$logo_path, $brand_name]
                );
                
                if ($updated) {
                    $updated_brands++;
                }
            } else {
                echo "❌ فشل في نسخ شعار $brand_name<br>";
            }
        } else {
            echo "⚠️ لم يتم العثور على شعار $brand_name (البحث عن: " . implode(', ', $possible_files) . ")<br>";
        }
    }
    
    echo "<h2>3. إضافة شعار افتراضي</h2>";
    
    // إنشاء شعار افتراضي بسيط
    $default_svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <rect width="100" height="100" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
    <text x="50" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">CAR</text>
    <text x="50" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">BRAND</text>
    <circle cx="30" cy="75" r="8" fill="#6c757d"/>
    <circle cx="70" cy="75" r="8" fill="#6c757d"/>
    <rect x="20" y="60" width="60" height="15" fill="none" stroke="#6c757d" stroke-width="2" rx="3"/>
</svg>';
    
    $default_logo_path = $target_logos_path . 'default-car.svg';
    if (file_put_contents($default_logo_path, $default_svg)) {
        echo "✅ تم إنشاء الشعار الافتراضي: default-car.svg<br>";
    }
    
    echo "<h2>✅ تم الانتهاء من التحديث</h2>";
    echo "<p><strong>النتائج:</strong></p>";
    echo "- تم نسخ $copied_files ملف شعار<br>";
    echo "- تم تحديث $updated_brands ماركة في قاعدة البيانات<br>";
    
    // عرض الماركات المحدثة
    echo "<h3>الماركات المحدثة:</h3>";
    $brands = $db->fetchAll("SELECT name, arabic_name, logo_path FROM car_brands WHERE logo_path IS NOT NULL ORDER BY name");
    
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">';
    foreach ($brands as $brand) {
        $logo_url = '../assets/images/car-brands/' . $brand['logo_path'];
        echo '<div style="border: 1px solid #ddd; padding: 10px; text-align: center; border-radius: 8px;">';
        echo '<img src="' . $logo_url . '" alt="' . $brand['name'] . '" style="width: 60px; height: 60px; object-fit: contain; margin-bottom: 10px;">';
        echo '<div><strong>' . $brand['name'] . '</strong></div>';
        echo '<div style="color: #666; font-size: 12px;">' . $brand['arabic_name'] . '</div>';
        echo '</div>';
    }
    echo '</div>';
    
    echo "<h3>اختبر الآن:</h3>";
    echo '<a href="../pages/home.php" class="btn btn-primary">الصفحة الرئيسية - قسم ماركات السيارات</a><br>';
    echo '<a href="../pages/search.php?brand=Toyota" class="btn btn-success">البحث بـ Toyota</a><br>';
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
