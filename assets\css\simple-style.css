/* ملف CSS بسيط لموقع حراجنا */

/* متغيرات CSS - نظام الألوان الموحد */
:root {
    /* اللون الأساسي الموحد - 90% من العناصر */
    --primary-color: #17a2b8;
    --primary-dark: #138496;

    /* ألوان التمييز - 7% من العناصر */
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;

    /* ألوان التنبيهات - 3% من العناصر */
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;

    /* ألوان إضافية للتوافق */
    --info-color: #17a2b8;
    --dark-color: #2c3e50;

    /* إعدادات التصميم */
    --border-radius: 10px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* الخط الأساسي */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fc;
    line-height: 1.6;
    direction: rtl;
}

/* شريط التنقل */
.navbar {
    background-color: #17a2b8 !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white !important;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.card-header {
    background-color: #17a2b8 !important;
    color: white;
    border: none;
    padding: 20px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

/* بطاقات الإعلانات */
.ad-card {
    position: relative;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.ad-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.ad-card .ad-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.ad-card .ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.ad-card .no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f8f9fa;
    color: #6c757d;
}

.ad-card .no-image i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.ad-card .ad-content {
    padding: 1rem;
}

.ad-card .ad-title {
    margin-bottom: 0.5rem;
}

.ad-card .ad-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
}

.ad-card .ad-title a:hover {
    color: var(--primary-color);
}

.ad-card .ad-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 0.5rem;
}

.ad-card .ad-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.ad-card .ad-category {
    font-size: 0.8rem;
    color: var(--primary-color);
}

/* بطاقات الفئات */
.category-card {
    display: block;
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    text-decoration: none;
    color: inherit;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    color: inherit;
    text-decoration: none;
}

.category-card .category-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.category-card .category-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-card .category-count {
    font-size: 0.9rem;
    color: #6c757d;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
}

.btn-primary {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3 !important;
    border-color: #0056b3 !important;
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
    color: white;
}

.btn-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white;
}

.btn-info:hover {
    background-color: #138496 !important;
    border-color: #138496 !important;
    color: white;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* النماذج */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 0.75rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 20px;
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* الفوتر */
.footer {
    background-color: #17a2b8 !important;
    color: white;
    padding: 3rem 0 1rem 0;
    margin-top: 4rem;
}

.footer h5 {
    color: white;
    margin-bottom: 1rem;
}

.footer a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer a:hover {
    color: white;
}

/* استجابة للجوال */
@media (max-width: 768px) {
    .ad-card .ad-image {
        height: 180px;
    }
    
    .category-card {
        padding: 20px;
    }
}

/* نظام الألوان الموحد - .bg-info للجميع */
.text-info { color: #17a2b8 !important; }
.bg-info { background-color: #17a2b8 !important; }
.border-info { border-color: #17a2b8 !important; }

/* توحيد جميع الألوان الأساسية لتستخدم .bg-info */
.text-primary { color: #17a2b8 !important; }
.bg-primary { background-color: #17a2b8 !important; }
.border-primary { border-color: #17a2b8 !important; }

.text-secondary { color: #17a2b8 !important; }
.bg-secondary { background-color: #17a2b8 !important; }
.border-secondary { border-color: #17a2b8 !important; }

/* الخلفيات البيضاء */
.text-light { color: #f8f9fa !important; }
.bg-light { background-color: #f8f9fa !important; }
.border-light { border-color: #f8f9fa !important; }

.text-white { color: #ffffff !important; }
.bg-white { background-color: #ffffff !important; }
.border-white { border-color: #ffffff !important; }

/* أزرار بيضاء */
.btn-white {
    background-color: #ffffff !important;
    border-color: #ffffff !important;
    color: #17a2b8 !important;
}

.btn-white:hover {
    background-color: #f8f9fa !important;
    border-color: #f8f9fa !important;
    color: #17a2b8 !important;
}

/* ألوان التنبيهات للحالات الخاصة فقط */
.text-success { color: #28a745 !important; }
.bg-success { background-color: #28a745 !important; }
.border-success { border-color: #28a745 !important; }

.text-warning { color: #ffc107 !important; }
.bg-warning { background-color: #ffc107 !important; }
.border-warning { border-color: #ffc107 !important; }

.text-danger { color: #dc3545 !important; }
.bg-danger { background-color: #dc3545 !important; }
.border-danger { border-color: #dc3545 !important; }

/* تحسينات للتصميم المتجاوب الجديد */
.hover-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.hover-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #007bff;
}

/* تحسينات للفئات الجانبية */
.category-sidebar .card-body {
    max-height: 500px;
    overflow-y: auto;
}

.category-sidebar .card-body::-webkit-scrollbar {
    width: 4px;
}

.category-sidebar .card-body::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.category-sidebar .card-body::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 2px;
}

.category-sidebar .card-body::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card-body {
        padding: 0.75rem;
    }

    .btn-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .h4 {
        font-size: 1.1rem;
    }

    .card-header h5 {
        font-size: 0.9rem;
    }

    /* إعادة ترتيب الأعمدة للجوال */
    .order-mobile-first {
        order: -1;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 5px;
        padding-right: 5px;
    }

    .card {
        margin-bottom: 0.75rem;
    }

    .card-body {
        padding: 0.5rem;
    }

    .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }

    /* تصغير Hero Section للجوال */
    .hero-section .card-body {
        padding: 1rem;
    }

    .hero-section .h4 {
        font-size: 1rem;
    }

    .hero-section p {
        font-size: 0.9rem;
    }
}

/* تحسينات للإحصائيات */
.stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    background: rgba(255,255,255,0.8);
}

/* تحسينات للنصوص */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* تحسينات للبطاقات الصغيرة */
.compact-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.compact-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات للشبكة */
.grid-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

@media (max-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
    }
}

@media (max-width: 576px) {
    .grid-responsive {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
    }
}
