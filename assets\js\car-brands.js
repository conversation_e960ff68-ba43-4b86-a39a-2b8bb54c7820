// متغير لتخزين بيانات السيارات
let carBrands = [];

// جلب بيانات السيارات من قاعدة البيانات
async function fetchCarBrands() {
    try {
        const response = await fetch('../api/get-car-brands.php');
        const data = await response.json();

        if (data.success) {
            carBrands = data.brands.map(brand => ({
                name: brand.name,
                arabic: brand.arabic_name,
                logo: brand.logo_path || 'default-car.svg',
                popular: brand.is_popular == 1,
                id: brand.id
            }));
        } else {
            console.error('خطأ في جلب بيانات السيارات:', data.message);
            // استخدام البيانات الافتراضية في حالة الخطأ
            carBrands = getDefaultCarBrands();
        }
    } catch (error) {
        console.error('خطأ في الاتصال:', error);
        // استخدام البيانات الافتراضية في حالة الخطأ
        carBrands = getDefaultCarBrands();
    }
}

// البيانات الافتراضية للسيارات (في حالة فشل جلب البيانات من قاعدة البيانات)
function getDefaultCarBrands() {
    return [
        { name: 'Toyota', arabic: 'تويوتا', logo: 'toyota-svgrepo-com.svg', popular: true },
        { name: 'Nissan', arabic: 'نيسان', logo: 'nissan-logo.svg', popular: true },
        { name: 'Honda', arabic: 'هوندا', logo: 'honda-svgrepo-com.svg', popular: true },
        { name: 'Hyundai', arabic: 'هيونداي', logo: 'hyundai-svgrepo-com.svg', popular: true },
        { name: 'Kia', arabic: 'كيا', logo: 'kia-svgrepo-com.svg', popular: true },
        { name: 'Chevrolet', arabic: 'شيفروليه', logo: 'chevrolet-svgrepo-com.svg', popular: true },
        { name: 'Ford', arabic: 'فورد', logo: 'ford-svgrepo-com.svg', popular: true },
        { name: 'Mercedes', arabic: 'مرسيدس', logo: 'mercedes-benz-logo-svgrepo-com.svg', popular: true },
        { name: 'Audi', arabic: 'أودي', logo: 'audi-svgrepo-com.svg', popular: true },
        { name: 'Lexus', arabic: 'لكزس', logo: 'lexus-logo-svgrepo-com.svg', popular: true },
        { name: 'Mazda', arabic: 'مازدا', logo: 'mazda-svgrepo-com.svg', popular: true },
        { name: 'Peugeot', arabic: 'بيجو', logo: 'peugeot-alt-svgrepo-com.svg', popular: false },
        { name: 'Renault', arabic: 'رينو', logo: 'renault-alt-svgrepo-com.svg', popular: false },
        { name: 'Mitsubishi', arabic: 'ميتسوبيشي', logo: 'mitsubishi-svgrepo-com.svg', popular: false },
        { name: 'Subaru', arabic: 'سوبارو', logo: 'subaru-svgrepo-com.svg', popular: false },
        { name: 'Suzuki', arabic: 'سوزوكي', logo: 'suzuki-svgrepo-com.svg', popular: false },
        { name: 'Acura', arabic: 'أكورا', logo: 'acura-svgrepo-com.svg', popular: false },
        { name: 'Cadillac', arabic: 'كاديلاك', logo: 'cadillac-svgrepo-com.svg', popular: false },
        { name: 'Infiniti', arabic: 'إنفينيتي', logo: 'infiniti-logo.svg', popular: false },
        { name: 'Acura', arabic: 'أكورا', logo: 'acura-logo.svg', popular: false },
        { name: 'Cadillac', arabic: 'كاديلاك', logo: 'cadillac-logo.svg', popular: false },
        { name: 'Jeep', arabic: 'جيب', logo: 'jeep-logo.svg', popular: false },
        { name: 'Land Rover', arabic: 'لاند روفر', logo: 'landrover-logo.svg', popular: false },
        { name: 'Porsche', arabic: 'بورش', logo: 'porsche-logo.svg', popular: false },
        { name: 'Jaguar', arabic: 'جاكوار', logo: 'jaguar-logo.svg', popular: false },
        { name: 'Volvo', arabic: 'فولفو', logo: 'volvo-logo.svg', popular: false },
        { name: 'Genesis', arabic: 'جينيسيس', logo: 'genesis-logo.svg', popular: false },
        { name: 'Tesla', arabic: 'تيسلا', logo: 'tesla-logo.svg', popular: false },
        { name: 'GMC', arabic: 'جي إم سي', logo: 'gmc-logo.svg', popular: false },
        { name: 'Dodge', arabic: 'دودج', logo: 'dodge-logo.svg', popular: false }
    ];
}

// إنشاء عنصر السيارة
function createCarBrandElement(brand) {
    const brandElement = document.createElement('a');
    brandElement.href = `search.php?brand=${encodeURIComponent(brand.name)}`;
    brandElement.className = 'car-brand-item';
    brandElement.innerHTML = `
        <div class="car-brand-logo">
            <img src="../assets/images/car-brands/${brand.logo}" 
                 alt="${brand.name}" 
                 onerror="this.src='../assets/images/car-brands/default-car.svg'">
        </div>
        <div class="car-brand-name">${brand.name}</div>
        <div class="car-brand-arabic">${brand.arabic}</div>
    `;
    
    if (!brand.popular) {
        brandElement.classList.add('hidden-brands');
    }
    
    return brandElement;
}

// تهيئة قسم السيارات
async function initCarBrands() {
    const container = document.getElementById('car-brands-container');
    if (!container) return;

    // جلب بيانات السيارات أولاً
    await fetchCarBrands();

    const grid = container.querySelector('.car-brands-grid');
    const showMoreBtn = container.querySelector('.show-more-btn');

    // مسح مؤشر التحميل
    const loadingSpinner = grid.querySelector('.loading-spinner');
    if (loadingSpinner) {
        loadingSpinner.remove();
    }

    // إضافة السيارات إلى الشبكة
    carBrands.forEach(brand => {
        const brandElement = createCarBrandElement(brand);
        grid.appendChild(brandElement);
    });

    // معالج زر "عرض المزيد"
    if (showMoreBtn) {
        showMoreBtn.addEventListener('click', function() {
            const hiddenBrands = container.querySelectorAll('.hidden-brands');
            const isShowing = hiddenBrands[0]?.classList.contains('show');

            hiddenBrands.forEach(brand => {
                if (isShowing) {
                    brand.classList.remove('show');
                } else {
                    brand.classList.add('show');
                }
            });

            this.textContent = isShowing ? 'عرض المزيد' : 'عرض أقل';
            this.innerHTML = isShowing ?
                '<i class="fas fa-chevron-down me-2"></i>عرض المزيد' :
                '<i class="fas fa-chevron-up me-2"></i>عرض أقل';
        });
    }
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initCarBrands);

// إضافة تأثيرات التحميل المتدرجة
function addLoadingEffects() {
    const brandItems = document.querySelectorAll('.car-brand-item');
    brandItems.forEach((item, index) => {
        // تأخير متدرج للتأثير البصري
        setTimeout(() => {
            item.style.animationDelay = `${index * 0.1}s`;
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// تأثير التمرير للعناصر المخفية
function handleScrollAnimation() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    const hiddenItems = document.querySelectorAll('.hidden-brands');
    hiddenItems.forEach(item => {
        observer.observe(item);
    });
}

// تشغيل التأثيرات بعد التحميل
setTimeout(() => {
    addLoadingEffects();
    handleScrollAnimation();
}, 300);
