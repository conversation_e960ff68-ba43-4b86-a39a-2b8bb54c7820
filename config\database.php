<?php
class Database {
    private $host = 'srv1513.hstgr.io';
    private $dbname = 'u302460181_hoasb';
    private $username = 'u302460181_hoasb';
    private $password = '10743211uU@';
    private $charset = 'utf8mb4';
    private $pdo;

    public function __construct() {
        $this->connect();
    }

    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
            try {
                $pdo_temp = new PDO("mysql:host={$this->host};charset={$this->charset}", $this->username, $this->password, $options);
                $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS {$this->dbname} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            } catch (PDOException $e2) {
                die('فشل في الاتصال بقاعدة البيانات: ' . $e2->getMessage());
            }
        }
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception('خطأ في قاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}

// إنشاء اتصال عام
$db = new Database();
?>
