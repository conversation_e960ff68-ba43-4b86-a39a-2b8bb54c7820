<?php
// إعدادات الإنتاج لـ Hostinger

// إعدادات قاعدة البيانات
define('DB_HOST', 'srv1513.hstgr.io');
define('DB_NAME', 'u302460181_hoasb');
define('DB_USER', 'u302460181_hoasb');
define('DB_PASS', '10743211uU@');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الموقع
define('SITE_URL', 'https://yourdomain.com'); // غير هذا إلى رابط موقعك
define('SITE_NAME', 'حراجنا');
define('SITE_DESCRIPTION', 'منصة الإعلانات المبوبة الأولى في المملكة العربية السعودية');

// إعدادات الأمان
define('SECURE_MODE', true);
define('FORCE_HTTPS', true);

// إعدادات الأخطاء للإنتاج
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

// إعدادات الجلسة
ini_set('session.cookie_secure', 1);
ini_set('session.cookie_httponly', 1);
ini_set('session.use_strict_mode', 1);

// إعدادات الرفع
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.hostinger.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // غير هذا
define('SMTP_PASSWORD', 'your_email_password'); // غير هذا
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'حراجنا');

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // ساعة واحدة

// إعدادات الضغط
if (extension_loaded('zlib')) {
    ob_start('ob_gzhandler');
}

// تحسين الأداء
if (function_exists('opcache_reset')) {
    opcache_reset();
}
?>
