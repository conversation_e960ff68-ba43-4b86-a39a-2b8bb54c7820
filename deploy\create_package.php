<?php
// إنشاء حزمة للرفع على Hostinger

echo "📦 إنشاء حزمة الرفع لموقع حراجنا\n";
echo "===================================\n\n";

$root_dir = dirname(__DIR__);
$package_name = 'harajuna_website_' . date('Y-m-d_H-i-s') . '.zip';

// قائمة الملفات والمجلدات المطلوبة
$files_to_include = [
    'index.php',
    '.htaccess',
    'robots.txt',
    '.gitignore',
    'config/',
    'pages/',
    'admin/',
    'assets/',
    'includes/',
    'install/',
    'uploads/',
    'logs/',
    'cache/'
];

// قائمة الملفات المستبعدة
$exclude_patterns = [
    '*.log',
    '.git/',
    '.vscode/',
    '.idea/',
    'node_modules/',
    'vendor/',
    'deploy/',
    '*.tmp',
    '*.bak'
];

echo "📁 الملفات المشمولة:\n";
foreach ($files_to_include as $item) {
    $path = $root_dir . '/' . $item;
    if (file_exists($path) || is_dir($path)) {
        echo "✅ $item\n";
    } else {
        echo "⚠️  $item - غير موجود\n";
    }
}

echo "\n🚫 الملفات المستبعدة:\n";
foreach ($exclude_patterns as $pattern) {
    echo "❌ $pattern\n";
}

echo "\n📋 معلومات الحزمة:\n";
echo "📦 اسم الملف: $package_name\n";
echo "📅 التاريخ: " . date('Y-m-d H:i:s') . "\n";
echo "🎯 الهدف: Hostinger Hosting\n";

echo "\n✅ تم إنشاء قائمة الملفات بنجاح!\n";
echo "\n📝 ملاحظات مهمة:\n";
echo "1. تأكد من تحديث رابط الموقع في config/production.php\n";
echo "2. تأكد من إعدادات قاعدة البيانات\n";
echo "3. احذف مجلد install بعد التثبيت\n";
echo "4. تأكد من صلاحيات المجلدات (755)\n";

echo "\n🚀 جاهز للرفع على Hostinger!\n";
?>
