<?php
// فحص نهائي قبل الرفع على Hostinger

echo "🔍 الفحص النهائي لموقع حراجنا\n";
echo "==============================\n\n";

$checks = [];
$warnings = [];
$errors = [];

// 1. فحص الملفات الأساسية
echo "1. فحص الملفات الأساسية:\n";
$essential_files = [
    'index.php' => 'الصفحة الرئيسية',
    '.htaccess' => 'إعدادات Apache',
    'config/database.php' => 'إعدادات قاعدة البيانات',
    'config/config.php' => 'إعدادات الموقع',
    'pages/home.php' => 'الصفحة الرئيسية',
    'admin/index.php' => 'لوحة الإدارة',
    'install/install.php' => 'ملف التثبيت'
];

foreach ($essential_files as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ $desc ($file)\n";
        $checks[] = $file;
    } else {
        echo "❌ $desc ($file) - مفقود!\n";
        $errors[] = $file;
    }
}

// 2. فحص إعدادات قاعدة البيانات
echo "\n2. فحص إعدادات قاعدة البيانات:\n";
$db_config = file_get_contents('config/database.php');
if (strpos($db_config, 'srv1513.hstgr.io') !== false) {
    echo "✅ Host محدث لـ Hostinger\n";
} else {
    echo "❌ Host غير محدث!\n";
    $errors[] = 'database_host';
}

if (strpos($db_config, 'u302460181_hoasb') !== false) {
    echo "✅ اسم قاعدة البيانات محدث\n";
} else {
    echo "❌ اسم قاعدة البيانات غير محدث!\n";
    $errors[] = 'database_name';
}

// 3. فحص الأمان
echo "\n3. فحص الأمان:\n";
$security_files = [
    'uploads/.htaccess' => 'حماية مجلد الرفع',
    'config/.htaccess' => 'حماية مجلد الإعدادات',
    'logs/.htaccess' => 'حماية مجلد السجلات'
];

foreach ($security_files as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ $desc\n";
    } else {
        echo "⚠️  $desc - غير موجود\n";
        $warnings[] = $file;
    }
}

// 4. فحص المجلدات المطلوبة
echo "\n4. فحص المجلدات:\n";
$required_dirs = [
    'uploads' => 'مجلد الرفع',
    'logs' => 'مجلد السجلات',
    'cache' => 'مجلد التخزين المؤقت'
];

foreach ($required_dirs as $dir => $desc) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ $desc - قابل للكتابة\n";
        } else {
            echo "⚠️  $desc - غير قابل للكتابة\n";
            $warnings[] = $dir . '_writable';
        }
    } else {
        echo "❌ $desc - غير موجود!\n";
        $errors[] = $dir;
    }
}

// 5. فحص ملف التثبيت
echo "\n5. فحص ملف التثبيت:\n";
$install_content = file_get_contents('install/install.php');
if (strpos($install_content, 'warning') !== false) {
    echo "✅ GD Extension أصبح اختياري\n";
} else {
    echo "⚠️  GD Extension لا يزال إجباري\n";
    $warnings[] = 'gd_extension';
}

// 6. فحص توحيد الألوان
echo "\n6. فحص توحيد الألوان:\n";
$css_content = file_get_contents('assets/css/simple-style.css');
if (strpos($css_content, 'bg-info') !== false) {
    echo "✅ نظام الألوان الموحد موجود\n";
} else {
    echo "⚠️  نظام الألوان الموحد غير مكتمل\n";
    $warnings[] = 'color_system';
}

// 7. فحص الصفحات الرئيسية
echo "\n7. فحص الصفحات:\n";
$pages = ['home.php', 'categories.php', 'about.php', 'contact.php'];
foreach ($pages as $page) {
    $page_content = file_get_contents("pages/$page");
    if (strpos($page_content, 'bg-info') !== false) {
        echo "✅ $page - ألوان موحدة\n";
    } else {
        echo "⚠️  $page - ألوان غير موحدة\n";
        $warnings[] = $page . '_colors';
    }
}

// النتيجة النهائية
echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 النتيجة النهائية:\n";
echo "✅ ملفات صحيحة: " . count($checks) . "\n";
echo "⚠️  تحذيرات: " . count($warnings) . "\n";
echo "❌ أخطاء: " . count($errors) . "\n";

if (empty($errors)) {
    echo "\n🎉 الموقع جاهز للرفع على Hostinger!\n";
    echo "\n📋 الخطوات التالية:\n";
    echo "1. ضغط جميع الملفات في ZIP\n";
    echo "2. رفع الملفات إلى public_html\n";
    echo "3. تشغيل install/install.php\n";
    echo "4. حذف مجلد install بعد التثبيت\n";
} else {
    echo "\n❌ يجب إصلاح الأخطاء التالية أولاً:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  تحذيرات (يمكن تجاهلها):\n";
    foreach ($warnings as $warning) {
        echo "- $warning\n";
    }
}

echo "\n🔗 معلومات الاتصال:\n";
echo "Host: srv1513.hstgr.io\n";
echo "Database: u302460181_hoasb\n";
echo "Username: u302460181_hoasb\n";
echo "Password: 10743211uU@\n";

echo "\n✅ انتهى الفحص!\n";
?>
