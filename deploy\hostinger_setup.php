<?php
// ملف تجهيز الموقع للرفع على Hostinger

echo "🚀 تجهيز موقع حراجنا للرفع على Hostinger\n";
echo "=====================================\n\n";

// 1. التحقق من الملفات المطلوبة
echo "1. التحقق من الملفات المطلوبة:\n";
$required_files = [
    'config/database.php',
    'config/config.php',
    'pages/home.php',
    'admin/index.php',
    'install/install.php',
    'assets/css/simple-style.css',
    '.htaccess'
];

foreach ($required_files as $file) {
    if (file_exists("../$file")) {
        echo "✅ $file\n";
    } else {
        echo "❌ $file - مفقود!\n";
    }
}

echo "\n2. إعدادات قاعدة البيانات:\n";
echo "✅ Host: srv1513.hstgr.io\n";
echo "✅ Database: u302460181_hoasb\n";
echo "✅ Username: u302460181_hoasb\n";
echo "✅ Password: محدد\n";

echo "\n3. الملفات المحدثة للإنتاج:\n";
echo "✅ config/database.php - محدث بإعدادات Hostinger\n";
echo "✅ config/production.php - ملف إعدادات الإنتاج\n";
echo "✅ .htaccess - محسن للأمان والأداء\n";
echo "✅ index.php - إعادة توجيه للصفحة الرئيسية\n";

echo "\n4. المجلدات المطلوبة:\n";
$required_dirs = ['uploads', 'logs', 'cache'];
foreach ($required_dirs as $dir) {
    if (!is_dir("../$dir")) {
        mkdir("../$dir", 0755, true);
        echo "✅ تم إنشاء مجلد $dir\n";
    } else {
        echo "✅ مجلد $dir موجود\n";
    }
}

echo "\n5. ملف .gitignore:\n";
$gitignore_content = "# ملفات النظام
.DS_Store
Thumbs.db

# ملفات الإعدادات الحساسة
config/local.php
.env

# مجلدات الرفع والتخزين المؤقت
uploads/*
!uploads/.htaccess
logs/*
!logs/.htaccess
cache/*
!cache/.htaccess

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo

# ملفات Node.js (إذا كانت موجودة)
node_modules/
npm-debug.log
yarn-error.log

# ملفات Composer (إذا كانت موجودة)
vendor/
composer.lock
";

file_put_contents('../.gitignore', $gitignore_content);
echo "✅ تم إنشاء ملف .gitignore\n";

echo "\n6. ملفات الحماية:\n";
// حماية مجلد uploads
$uploads_htaccess = "# منع تنفيذ ملفات PHP
<Files *.php>
    Order Deny,Allow
    Deny from all
</Files>

# السماح بالصور فقط
<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>
";
file_put_contents('../uploads/.htaccess', $uploads_htaccess);
echo "✅ تم إنشاء حماية مجلد uploads\n";

// حماية مجلد logs
$logs_htaccess = "Order Deny,Allow\nDeny from all";
file_put_contents('../logs/.htaccess', $logs_htaccess);
echo "✅ تم إنشاء حماية مجلد logs\n";

// حماية مجلد config
$config_htaccess = "Order Deny,Allow\nDeny from all";
file_put_contents('../config/.htaccess', $config_htaccess);
echo "✅ تم إنشاء حماية مجلد config\n";

echo "\n7. تحسين الأداء:\n";
// ملف robots.txt
$robots_content = "User-agent: *
Allow: /

# السماح للمحركات بفهرسة الصفحات المهمة
Allow: /pages/
Allow: /assets/

# منع فهرسة المجلدات الحساسة
Disallow: /admin/
Disallow: /config/
Disallow: /includes/
Disallow: /install/
Disallow: /logs/
Disallow: /uploads/

# خريطة الموقع (إذا كانت متوفرة)
Sitemap: https://yourdomain.com/sitemap.xml
";
file_put_contents('../robots.txt', $robots_content);
echo "✅ تم إنشاء ملف robots.txt\n";

echo "\n8. التحقق من الأمان:\n";
echo "✅ حماية الملفات الحساسة\n";
echo "✅ منع عرض قائمة الملفات\n";
echo "✅ إعادة توجيه HTTPS\n";
echo "✅ حماية من الهجمات الشائعة\n";

echo "\n🎉 تم تجهيز الموقع بنجاح للرفع على Hostinger!\n\n";

echo "📋 خطوات الرفع:\n";
echo "1. ضغط جميع الملفات في ملف ZIP\n";
echo "2. رفع الملفات إلى public_html في Hostinger\n";
echo "3. إنشاء قاعدة البيانات في cPanel\n";
echo "4. تشغيل install/install.php\n";
echo "5. حذف مجلد install بعد التثبيت\n\n";

echo "🔗 روابط مهمة بعد الرفع:\n";
echo "- الموقع: https://yourdomain.com\n";
echo "- التثبيت: https://yourdomain.com/install/install.php\n";
echo "- لوحة الإدارة: https://yourdomain.com/admin/\n\n";

echo "✅ جاهز للرفع!\n";
?>
