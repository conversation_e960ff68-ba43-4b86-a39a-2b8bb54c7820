<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'title' => sanitize($_POST['title'] ?? ''),
        'description' => sanitize($_POST['description'] ?? ''),
        'category_id' => (int)($_POST['category_id'] ?? 0),
        'car_brand_id' => (int)($_POST['car_brand_id'] ?? 0),
        'price' => (float)($_POST['price'] ?? 0),
        'price_type' => sanitize($_POST['price_type'] ?? 'fixed'),
        'condition_type' => sanitize($_POST['condition_type'] ?? 'used'),
        'city' => sanitize($_POST['city'] ?? ''),
        'region' => sanitize($_POST['region'] ?? ''),
        'contact_phone' => sanitize($_POST['contact_phone'] ?? ''),
        'contact_email' => sanitize($_POST['contact_email'] ?? ''),
        'whatsapp' => sanitize($_POST['whatsapp'] ?? ''),
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['title']) || empty($data['description']) || $data['category_id'] <= 0) {
        $error = 'العنوان والوصف والفئة مطلوبة';
    } elseif (strlen($data['title']) < 10) {
        $error = 'العنوان يجب أن يكون 10 أحرف على الأقل';
    } elseif (strlen($data['description']) < 20) {
        $error = 'الوصف يجب أن يكون 20 حرف على الأقل';
    } elseif ($data['price'] < 0) {
        $error = 'السعر لا يمكن أن يكون سالباً';
    } elseif (!empty($data['contact_phone']) && !validatePhone($data['contact_phone'])) {
        $error = 'رقم الهاتف غير صحيح';
    } elseif (!empty($data['contact_email']) && !validateEmail($data['contact_email'])) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            // التحقق من وجود عمود car_brand_id
            $columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'car_brand_id'");
            $has_car_brand_column = !empty($columns);

            if ($has_car_brand_column) {
                // إدراج الإعلان مع ماركة السيارة (نشط تلقائياً)
                $sql = "INSERT INTO ads (user_id, category_id, car_brand_id, title, description, price, price_type, condition_type,
                                       city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";

                $db->query($sql, [
                    $_SESSION['user_id'],
                    $data['category_id'],
                    $data['car_brand_id'] ?: null,
                    $data['title'],
                    $data['description'],
                    $data['price'],
                    $data['price_type'],
                    $data['condition_type'],
                    $data['city'],
                    $data['region'],
                    $data['contact_phone'],
                    $data['contact_email'],
                    $data['whatsapp']
                ]);
            } else {
                // إدراج الإعلان بدون ماركة السيارة (للتوافق مع قاعدة البيانات القديمة)
                $sql = "INSERT INTO ads (user_id, category_id, title, description, price, price_type, condition_type,
                                       city, region, contact_phone, contact_email, whatsapp, status, expires_at, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW())";

                $db->query($sql, [
                    $_SESSION['user_id'],
                    $data['category_id'],
                    $data['title'],
                    $data['description'],
                    $data['price'],
                    $data['price_type'],
                    $data['condition_type'],
                    $data['city'],
                    $data['region'],
                    $data['contact_phone'],
                    $data['contact_email'],
                    $data['whatsapp']
                ]);
            }
            
            $ad_id = $db->lastInsertId();
            
            // رفع الصور (اختياري)
            if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
                $uploaded_images = 0;
                $max_images = 5;

                // التأكد من وجود مجلد الرفع
                $upload_dir = UPLOAD_PATH . 'ads/';
                if (!is_dir($upload_dir)) {
                    if (!mkdir($upload_dir, 0755, true)) {
                        error_log('Failed to create upload directory: ' . $upload_dir);
                        // لا نرمي خطأ هنا، فقط نسجل الخطأ ونتابع بدون صور
                    }
                }

                if (is_dir($upload_dir) && is_writable($upload_dir)) {
                    for ($i = 0; $i < count($_FILES['images']['name']) && $uploaded_images < $max_images; $i++) {
                        if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['images']['name'][$i],
                                'type' => $_FILES['images']['type'][$i],
                                'tmp_name' => $_FILES['images']['tmp_name'][$i],
                                'error' => $_FILES['images']['error'][$i],
                                'size' => $_FILES['images']['size'][$i]
                            ];

                            $image_path = uploadImage($file, 'ads');
                            if ($image_path) {
                                $is_primary = ($uploaded_images === 0) ? 1 : 0;

                                try {
                                    $sql = "INSERT INTO ad_images (ad_id, image_path, is_primary, sort_order) VALUES (?, ?, ?, ?)";
                                    $db->query($sql, [$ad_id, $image_path, $is_primary, $uploaded_images]);
                                    $uploaded_images++;
                                } catch (Exception $img_e) {
                                    error_log('Failed to save image to database: ' . $img_e->getMessage());
                                    // حذف الصورة المرفوعة إذا فشل حفظها في قاعدة البيانات
                                    if (file_exists(UPLOAD_PATH . $image_path)) {
                                        unlink(UPLOAD_PATH . $image_path);
                                    }
                                }
                            } else {
                                error_log('Failed to upload image: ' . $_FILES['images']['name'][$i]);
                            }
                        } else {
                            error_log('Image upload error: ' . $_FILES['images']['error'][$i] . ' for file: ' . $_FILES['images']['name'][$i]);
                        }
                    }
                }
            }
            
            $_SESSION['success'] = 'تم نشر الإعلان بنجاح';
            header('Location: my-ads.php');
            exit();
            
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء إضافة الإعلان: ' . $e->getMessage();
            error_log('Error in add-ad.php: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
        }
    }
}

// جلب الفئات وماركات السيارات
$categories = $db->fetchAll("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY sort_order");
$subcategories = $db->fetchAll("SELECT * FROM categories WHERE parent_id IS NOT NULL ORDER BY parent_id, sort_order");

// جلب ماركات السيارات (مع معالجة عدم وجود الجدول)
$car_brands = [];
try {
    $car_brands = $db->fetchAll("SELECT * FROM car_brands WHERE is_active = 1 ORDER BY is_popular DESC, sort_order ASC, name ASC");
} catch (Exception $e) {
    // إذا لم يكن جدول car_brands موجوداً، نستخدم قائمة افتراضية
    error_log('car_brands table not found: ' . $e->getMessage());
    $car_brands = [
        ['id' => 1, 'name' => 'Toyota', 'arabic_name' => 'تويوتا'],
        ['id' => 2, 'name' => 'Nissan', 'arabic_name' => 'نيسان'],
        ['id' => 3, 'name' => 'Honda', 'arabic_name' => 'هوندا'],
        ['id' => 4, 'name' => 'Hyundai', 'arabic_name' => 'هيونداي'],
        ['id' => 5, 'name' => 'Kia', 'arabic_name' => 'كيا']
    ];
}

$page_title = 'إضافة إعلان جديد';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0 rounded-lg mt-4">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-4">
                        <i class="fas fa-plus-circle"></i> إضافة إعلان جديد
                    </h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" enctype="multipart/form-data" id="addAdForm">
                        <!-- معلومات أساسية -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3"><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="title" name="title" 
                                   placeholder="عنوان الإعلان" maxlength="200" 
                                   value="<?= htmlspecialchars($data['title'] ?? '') ?>" required>
                            <label for="title">عنوان الإعلان *</label>
                            <div class="form-text">يجب أن يكون واضحاً ووصفياً (10 أحرف على الأقل)</div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="description" name="description" 
                                      placeholder="وصف الإعلان" style="height: 120px" maxlength="1000" required><?= htmlspecialchars($data['description'] ?? '') ?></textarea>
                            <label for="description">وصف الإعلان *</label>
                            <div class="form-text">اكتب وصفاً مفصلاً للمنتج أو الخدمة (20 حرف على الأقل)</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>" <?= ($data['category_id'] ?? 0) == $category['id'] ? 'selected' : '' ?>>
                                                <?= $category['name'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="category_id">الفئة *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="subcategory_id" name="subcategory_id">
                                        <option value="">اختر الفئة الفرعية</option>
                                    </select>
                                    <label for="subcategory_id">الفئة الفرعية</label>
                                </div>
                            </div>
                        </div>

                        <!-- ماركة السيارة (تظهر للفئات المتعلقة بالسيارات) -->
                        <div class="row" id="car-brand-section" style="display: none;">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="car_brand_id" name="car_brand_id">
                                        <option value="">اختر ماركة السيارة</option>
                                        <?php foreach ($car_brands as $brand): ?>
                                            <option value="<?= $brand['id'] ?>" <?= ($data['car_brand_id'] ?? 0) == $brand['id'] ? 'selected' : '' ?>>
                                                <?= $brand['name'] ?> - <?= $brand['arabic_name'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="car_brand_id">ماركة السيارة</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- السعر والحالة -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-tag"></i> السعر والحالة</h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="price" name="price" 
                                           placeholder="السعر" min="0" step="0.01" 
                                           value="<?= $data['price'] ?? '' ?>">
                                    <label for="price">السعر (ريال)</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="price_type" name="price_type">
                                        <option value="fixed" <?= ($data['price_type'] ?? 'fixed') === 'fixed' ? 'selected' : '' ?>>سعر ثابت</option>
                                        <option value="negotiable" <?= ($data['price_type'] ?? '') === 'negotiable' ? 'selected' : '' ?>>قابل للتفاوض</option>
                                        <option value="free" <?= ($data['price_type'] ?? '') === 'free' ? 'selected' : '' ?>>مجاني</option>
                                    </select>
                                    <label for="price_type">نوع السعر</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <select class="form-select" id="condition_type" name="condition_type">
                                <option value="new" <?= ($data['condition_type'] ?? 'used') === 'new' ? 'selected' : '' ?>>جديد</option>
                                <option value="used" <?= ($data['condition_type'] ?? 'used') === 'used' ? 'selected' : '' ?>>مستعمل</option>
                                <option value="refurbished" <?= ($data['condition_type'] ?? '') === 'refurbished' ? 'selected' : '' ?>>مجدد</option>
                            </select>
                            <label for="condition_type">حالة المنتج</label>
                        </div>
                        
                        <!-- الموقع -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-map-marker-alt"></i> الموقع</h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="region" name="region" required>
                                        <option value="">اختر المنطقة</option>
                                        <?php
                                        global $regions;
                                        foreach ($regions as $region => $cities):
                                        ?>
                                            <option value="<?= $region ?>" <?= ($data['region'] ?? '') === $region ? 'selected' : '' ?>>
                                                <?= $region ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="region">المنطقة *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="city" name="city" required>
                                        <option value="">اختر المدينة</option>
                                    </select>
                                    <label for="city">المدينة *</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- معلومات التواصل -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-phone"></i> معلومات التواصل</h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                           placeholder="رقم الهاتف" 
                                           value="<?= htmlspecialchars($data['contact_phone'] ?? '') ?>">
                                    <label for="contact_phone">رقم الهاتف</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           placeholder="البريد الإلكتروني" 
                                           value="<?= htmlspecialchars($data['contact_email'] ?? '') ?>">
                                    <label for="contact_email">البريد الإلكتروني</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="whatsapp" name="whatsapp" 
                                           placeholder="رقم الواتساب" 
                                           value="<?= htmlspecialchars($data['whatsapp'] ?? '') ?>">
                                    <label for="whatsapp">رقم الواتساب</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الصور -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-images"></i> الصور</h5>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="images" class="form-label">اختر الصور (حد أقصى 5 صور)</label>
                            <input type="file" class="form-control" id="images" name="images[]" 
                                   accept="image/*" multiple>
                            <div class="form-text">
                                الصور المدعومة: JPG, PNG, GIF (حد أقصى 5 ميجابايت لكل صورة)
                            </div>
                        </div>
                        
                        <div id="imagePreview" class="row"></div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="home.php" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus-circle"></i> إضافة الإعلان
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات المناطق والمدن
const regions = <?= json_encode($regions) ?>;
const subcategories = <?= json_encode($subcategories) ?>;

document.addEventListener('DOMContentLoaded', function() {
    const regionSelect = document.getElementById('region');
    const citySelect = document.getElementById('city');
    const categorySelect = document.getElementById('category_id');
    const subcategorySelect = document.getElementById('subcategory_id');
    const priceTypeSelect = document.getElementById('price_type');
    const priceInput = document.getElementById('price');
    const imagesInput = document.getElementById('images');
    const form = document.getElementById('addAdForm');
    
    // تحديث المدن عند تغيير المنطقة
    regionSelect.addEventListener('change', function() {
        const selectedRegion = this.value;
        citySelect.innerHTML = '<option value="">اختر المدينة</option>';
        
        if (selectedRegion && regions[selectedRegion]) {
            regions[selectedRegion].forEach(function(city) {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                citySelect.appendChild(option);
            });
        }
    });
    
    // تحديث الفئات الفرعية عند تغيير الفئة الرئيسية
    categorySelect.addEventListener('change', function() {
        const selectedCategory = parseInt(this.value);
        subcategorySelect.innerHTML = '<option value="">اختر الفئة الفرعية</option>';

        // إظهار/إخفاء قسم ماركة السيارة
        const carBrandSection = document.getElementById('car-brand-section');
        const categoryName = this.options[this.selectedIndex]?.text?.toLowerCase() || '';

        // إظهار قسم ماركة السيارة للفئات المتعلقة بالسيارات
        if (categoryName.includes('سيارات') || categoryName.includes('مركبات') || categoryName.includes('سيارة')) {
            carBrandSection.style.display = 'block';
        } else {
            carBrandSection.style.display = 'none';
            document.getElementById('car_brand_id').value = '';
        }

        if (selectedCategory) {
            const relatedSubcategories = subcategories.filter(sub => sub.parent_id == selectedCategory);
            relatedSubcategories.forEach(function(subcategory) {
                const option = document.createElement('option');
                option.value = subcategory.id;
                option.textContent = subcategory.name;
                subcategorySelect.appendChild(option);
            });
        }
    });
    
    // إخفاء/إظهار حقل السعر حسب نوع السعر
    priceTypeSelect.addEventListener('change', function() {
        if (this.value === 'free') {
            priceInput.value = '0';
            priceInput.disabled = true;
        } else {
            priceInput.disabled = false;
        }
    });
    
    // معاينة الصور
    imagesInput.addEventListener('change', function() {
        const files = this.files;
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = '';
        
        if (files.length > 5) {
            alert('يمكنك رفع 5 صور كحد أقصى');
            this.value = '';
            return;
        }
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            if (file.size > 5 * 1024 * 1024) {
                alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
                continue;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const col = document.createElement('div');
                col.className = 'col-md-3 mb-3';
                col.innerHTML = `
                    <div class="card">
                        <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;">
                        <div class="card-body p-2">
                            <small class="text-muted">${file.name}</small>
                        </div>
                    </div>
                `;
                preview.appendChild(col);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // التحقق من صحة النموذج
    form.addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        const categoryId = document.getElementById('category_id').value;
        const region = document.getElementById('region').value;
        const city = document.getElementById('city').value;
        
        if (title.length < 10) {
            e.preventDefault();
            showNotification('العنوان يجب أن يكون 10 أحرف على الأقل', 'error');
            return;
        }
        
        if (description.length < 20) {
            e.preventDefault();
            showNotification('الوصف يجب أن يكون 20 حرف على الأقل', 'error');
            return;
        }
        
        if (!categoryId) {
            e.preventDefault();
            showNotification('يرجى اختيار الفئة', 'error');
            return;
        }
        
        if (!region || !city) {
            e.preventDefault();
            showNotification('يرجى اختيار المنطقة والمدينة', 'error');
            return;
        }
        
        // إظهار مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إضافة الإعلان...';
    });
    
    // تحديد المدينة المحفوظة
    const savedRegion = '<?= $data['region'] ?? '' ?>';
    const savedCity = '<?= $data['city'] ?? '' ?>';
    if (savedRegion) {
        regionSelect.dispatchEvent(new Event('change'));
        setTimeout(() => {
            citySelect.value = savedCity;
        }, 100);
    }
    
    // تحديد الفئة الفرعية المحفوظة
    const savedCategory = <?= $data['category_id'] ?? 0 ?>;
    if (savedCategory) {
        categorySelect.dispatchEvent(new Event('change'));
    }
    
    // عداد الأحرف للنصوص
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    
    function addCharacterCounter(input, maxLength) {
        const counter = document.createElement('small');
        counter.className = 'text-muted';
        input.parentElement.appendChild(counter);
        
        function updateCounter() {
            const currentLength = input.value.length;
            counter.textContent = `${currentLength}/${maxLength}`;
            
            if (currentLength > maxLength * 0.9) {
                counter.className = 'text-warning';
            } else {
                counter.className = 'text-muted';
            }
        }
        
        input.addEventListener('input', updateCounter);
        updateCounter();
    }
    
    addCharacterCounter(titleInput, 200);
    addCharacterCounter(descriptionInput, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
