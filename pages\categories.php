<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// جلب الفئات الرئيسية مع عدد الإعلانات
// التحقق من وجود أعمدة الإعلانات المباعة
try {
    $sold_columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'is_sold'");
    $has_sold_columns = !empty($sold_columns);
} catch (Exception $e) {
    $has_sold_columns = false;
}

if ($has_sold_columns) {
    // مع أعمدة الإعلانات المباعة
    $categories = $db->fetchAll("
        SELECT c.*,
               (SELECT COUNT(*) FROM ads a WHERE a.category_id = c.id AND (a.status = 'active' OR (a.is_sold = 1 AND a.hide_sold_at > NOW()))) as direct_ads_count,
               (SELECT COUNT(*) FROM ads a
                JOIN categories sc ON a.category_id = sc.id
                WHERE sc.parent_id = c.id AND (a.status = 'active' OR (a.is_sold = 1 AND a.hide_sold_at > NOW()))) as subcategory_ads_count,
               (SELECT COUNT(*) FROM categories sc WHERE sc.parent_id = c.id) as subcategories_count
        FROM categories c
        WHERE c.parent_id IS NULL AND c.is_active = 1
        ORDER BY c.sort_order, c.name
    ");
} else {
    // بدون أعمدة الإعلانات المباعة
    $categories = $db->fetchAll("
        SELECT c.*,
               (SELECT COUNT(*) FROM ads a WHERE a.category_id = c.id AND a.status = 'active') as direct_ads_count,
               (SELECT COUNT(*) FROM ads a
                JOIN categories sc ON a.category_id = sc.id
                WHERE sc.parent_id = c.id AND a.status = 'active') as subcategory_ads_count,
               (SELECT COUNT(*) FROM categories sc WHERE sc.parent_id = c.id) as subcategories_count
        FROM categories c
        WHERE c.parent_id IS NULL AND c.is_active = 1
        ORDER BY c.sort_order, c.name
    ");
}

// جلب الفئات الفرعية
if ($has_sold_columns) {
    $subcategories = $db->fetchAll("
        SELECT c.*,
               (SELECT COUNT(*) FROM ads a WHERE a.category_id = c.id AND (a.status = 'active' OR (a.is_sold = 1 AND a.hide_sold_at > NOW()))) as ads_count,
               p.name as parent_name
        FROM categories c
        JOIN categories p ON c.parent_id = p.id
        WHERE c.is_active = 1
        ORDER BY c.parent_id, c.sort_order, c.name
    ");
} else {
    $subcategories = $db->fetchAll("
        SELECT c.*,
               (SELECT COUNT(*) FROM ads a WHERE a.category_id = c.id AND a.status = 'active') as ads_count,
               p.name as parent_name
        FROM categories c
        JOIN categories p ON c.parent_id = p.id
        WHERE c.is_active = 1
        ORDER BY c.parent_id, c.sort_order, c.name
    ");
}

// تجميع الفئات الفرعية حسب الفئة الرئيسية
$grouped_subcategories = [];
foreach ($subcategories as $sub) {
    $grouped_subcategories[$sub['parent_id']][] = $sub;
}

$page_title = 'الفئات';
include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4"><i class="fas fa-th-large"></i> جميع الفئات</h2>
            
            <!-- الفئات الرئيسية -->
            <div class="row">
                <?php foreach ($categories as $category): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm category-main-card">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="category-icon me-3">
                                        <i class="<?= $category['icon'] ?? 'fas fa-tag' ?> fa-2x text-info"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-1">
                                            <a href="category-ads.php?category=<?= $category['id'] ?>" class="text-decoration-none">
                                                <?= $category['name'] ?>
                                            </a>
                                        </h5>
                                        <div class="text-muted small">
                                            <?= number_format($category['direct_ads_count'] + $category['subcategory_ads_count']) ?> إعلان
                                            <?php if ($category['subcategories_count'] > 0): ?>
                                                • <?= $category['subcategories_count'] ?> فئة فرعية
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <?php if (!empty($category['description'])): ?>
                                    <p class="card-text text-muted small mb-3">
                                        <?= $category['description'] ?>
                                    </p>
                                <?php endif; ?>

                                <!-- الفئات الفرعية -->
                                <?php if (isset($grouped_subcategories[$category['id']])): ?>
                                    <div class="subcategories">
                                        <h6 class="text-muted mb-2">الفئات الفرعية:</h6>
                                        <div class="row">
                                            <?php foreach (array_slice($grouped_subcategories[$category['id']], 0, 6) as $sub): ?>
                                                <div class="col-6 mb-2">
                                                    <a href="category-ads.php?category=<?= $sub['id'] ?>"
                                                       class="btn btn-info btn-sm w-100 text-start text-white">
                                                        <?= $sub['name'] ?>
                                                        <span class="badge bg-white text-info ms-1"><?= $sub['ads_count'] ?></span>
                                                    </a>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        
                                        <?php if (count($grouped_subcategories[$category['id']]) > 6): ?>
                                            <div class="text-center mt-2">
                                                <button class="btn btn-link btn-sm show-more-subs" 
                                                        data-category="<?= $category['id'] ?>">
                                                    عرض المزيد (<?= count($grouped_subcategories[$category['id']]) - 6 ?>)
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="d-grid mt-3">
                                    <a href="category-ads.php?category=<?= $category['id'] ?>" class="btn btn-info">
                                        <i class="fas fa-eye"></i> عرض الإعلانات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mt-5">
                <div class="col-12">
                    <h3 class="mb-4">إحصائيات الفئات</h3>
                </div>
                
                <?php
                // جلب أكثر الفئات نشاطاً
                if ($has_sold_columns) {
                    $top_categories = $db->fetchAll("
                        SELECT c.name, c.id,
                               (SELECT COUNT(*) FROM ads a WHERE a.category_id = c.id AND (a.status = 'active' OR (a.is_sold = 1 AND a.hide_sold_at > NOW()))) +
                               (SELECT COUNT(*) FROM ads a
                                JOIN categories sc ON a.category_id = sc.id
                                WHERE sc.parent_id = c.id AND (a.status = 'active' OR (a.is_sold = 1 AND a.hide_sold_at > NOW()))) as ads_count
                        FROM categories c
                        WHERE c.parent_id IS NULL AND c.is_active = 1
                        ORDER BY ads_count DESC
                        LIMIT 5
                    ");
                } else {
                    $top_categories = $db->fetchAll("
                        SELECT c.name, c.id,
                               (SELECT COUNT(*) FROM ads a WHERE a.category_id = c.id AND a.status = 'active') +
                               (SELECT COUNT(*) FROM ads a
                                JOIN categories sc ON a.category_id = sc.id
                                WHERE sc.parent_id = c.id AND a.status = 'active') as ads_count
                        FROM categories c
                        WHERE c.parent_id IS NULL AND c.is_active = 1
                        ORDER BY ads_count DESC
                        LIMIT 5
                    ");
                }
                ?>

                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> أكثر الفئات نشاطاً</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($top_categories as $index => $cat): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-info me-2"><?= $index + 1 ?></span>
                                        <a href="category-ads.php?category=<?= $cat['id'] ?>" class="text-decoration-none">
                                            <?= $cat['name'] ?>
                                        </a>
                                    </div>
                                    <span class="badge bg-info text-white">
                                        <?= number_format($cat['ads_count']) ?> إعلان
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات عامة</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $general_stats = [
                                'total_categories' => $db->fetch("SELECT COUNT(*) as count FROM categories WHERE parent_id IS NULL AND is_active = 1")['count'],
                                'total_subcategories' => $db->fetch("SELECT COUNT(*) as count FROM categories WHERE parent_id IS NOT NULL AND is_active = 1")['count'],
                                'total_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'],
                                'categories_with_ads' => $db->fetch("SELECT COUNT(DISTINCT category_id) as count FROM ads WHERE status = 'active'")['count']
                            ];
                            ?>
                            
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="h4 text-info"><?= $general_stats['total_categories'] ?></div>
                                    <small class="text-info">فئة رئيسية</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="h4 text-info"><?= $general_stats['total_subcategories'] ?></div>
                                    <small class="text-info">فئة فرعية</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 text-info"><?= number_format($general_stats['total_ads']) ?></div>
                                    <small class="text-info">إجمالي الإعلانات</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 text-info"><?= $general_stats['categories_with_ads'] ?></div>
                                    <small class="text-info">فئة بها إعلانات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- دعوة للعمل -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center py-5">
                            <h3>لم تجد ما تبحث عنه؟</h3>
                            <p class="lead">أضف إعلانك الآن وابدأ في البيع</p>
                            <a href="add-ad.php" class="btn btn-white text-info btn-lg">
                                <i class="fas fa-plus"></i> أضف إعلان جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للفئات الفرعية الإضافية -->
<div class="modal fade" id="subcategoriesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">جميع الفئات الفرعية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="subcategoriesContent">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>
</div>

<style>
.category-main-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-main-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.category-icon {
    transition: transform 0.3s ease;
}

.category-main-card:hover .category-icon {
    transform: scale(1.1);
}

.subcategories .btn {
    transition: all 0.2s ease;
}

.subcategories .btn:hover {
    transform: translateX(-3px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // عرض المزيد من الفئات الفرعية
    document.querySelectorAll('.show-more-subs').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-category');
            showAllSubcategories(categoryId);
        });
    });
});

function showAllSubcategories(categoryId) {
    // البيانات محفوظة في PHP
    const allSubcategories = <?= json_encode($grouped_subcategories) ?>;
    const subcategories = allSubcategories[categoryId] || [];
    
    let content = '<div class="row">';
    subcategories.forEach(function(sub) {
        content += `
            <div class="col-md-4 mb-2">
                <a href="category-ads.php?category=${sub.id}" class="btn btn-info btn-sm w-100 text-start text-white">
                    ${sub.name}
                    <span class="badge bg-white text-info ms-1">${sub.ads_count}</span>
                </a>
            </div>
        `;
    });
    content += '</div>';
    
    document.getElementById('subcategoriesContent').innerHTML = content;
    
    const modal = new bootstrap.Modal(document.getElementById('subcategoriesModal'));
    modal.show();
}
</script>

<?php include 'includes/footer.php'; ?>
