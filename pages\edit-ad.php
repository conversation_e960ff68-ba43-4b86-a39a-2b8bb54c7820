<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// جلب معرف الإعلان
$ad_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($ad_id <= 0) {
    header('Location: my-ads.php');
    exit();
}

// التحقق من ملكية الإعلان
$ad = $db->fetch("SELECT * FROM ads WHERE id = ? AND user_id = ?", [$ad_id, $_SESSION['user_id']]);

if (!$ad) {
    $_SESSION['error'] = 'الإعلان غير موجود أو ليس لديك صلاحية لتعديله';
    header('Location: my-ads.php');
    exit();
}

$error = '';
$success = '';

// معالجة تحديث الإعلان
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'title' => sanitize($_POST['title'] ?? ''),
        'description' => sanitize($_POST['description'] ?? ''),
        'category_id' => (int)($_POST['category_id'] ?? 0),
        'price' => (float)($_POST['price'] ?? 0),
        'price_type' => sanitize($_POST['price_type'] ?? 'fixed'),
        'condition_type' => sanitize($_POST['condition_type'] ?? 'used'),
        'city' => sanitize($_POST['city'] ?? ''),
        'region' => sanitize($_POST['region'] ?? ''),
        'contact_phone' => sanitize($_POST['contact_phone'] ?? ''),
        'contact_email' => sanitize($_POST['contact_email'] ?? ''),
        'whatsapp' => sanitize($_POST['whatsapp'] ?? ''),
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['title']) || empty($data['description']) || $data['category_id'] <= 0) {
        $error = 'العنوان والوصف والفئة مطلوبة';
    } elseif (strlen($data['title']) < 10) {
        $error = 'العنوان يجب أن يكون 10 أحرف على الأقل';
    } elseif (strlen($data['description']) < 20) {
        $error = 'الوصف يجب أن يكون 20 حرف على الأقل';
    } elseif ($data['price'] < 0) {
        $error = 'السعر لا يمكن أن يكون سالباً';
    } elseif (!empty($data['contact_phone']) && !validatePhone($data['contact_phone'])) {
        $error = 'رقم الهاتف غير صحيح';
    } elseif (!empty($data['contact_email']) && !validateEmail($data['contact_email'])) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            // تحديث الإعلان
            $sql = "UPDATE ads SET 
                        title = ?, description = ?, category_id = ?, price = ?, price_type = ?, 
                        condition_type = ?, city = ?, region = ?, contact_phone = ?, 
                        contact_email = ?, whatsapp = ?, status = 'pending', updated_at = NOW() 
                    WHERE id = ? AND user_id = ?";
            
            $db->query($sql, [
                $data['title'], $data['description'], $data['category_id'], $data['price'],
                $data['price_type'], $data['condition_type'], $data['city'], $data['region'],
                $data['contact_phone'], $data['contact_email'], $data['whatsapp'],
                $ad_id, $_SESSION['user_id']
            ]);
            
            // رفع صور جديدة إذا تم اختيارها
            if (!empty($_FILES['images']['name'][0])) {
                $uploaded_images = 0;
                $max_images = 5;
                
                // عدد الصور الحالية
                $current_images_count = $db->fetch("SELECT COUNT(*) as count FROM ad_images WHERE ad_id = ?", [$ad_id])['count'];
                
                for ($i = 0; $i < count($_FILES['images']['name']) && ($current_images_count + $uploaded_images) < $max_images; $i++) {
                    if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                        $file = [
                            'name' => $_FILES['images']['name'][$i],
                            'type' => $_FILES['images']['type'][$i],
                            'tmp_name' => $_FILES['images']['tmp_name'][$i],
                            'error' => $_FILES['images']['error'][$i],
                            'size' => $_FILES['images']['size'][$i]
                        ];
                        
                        $image_path = uploadImage($file, 'ads');
                        if ($image_path) {
                            $is_primary = ($current_images_count === 0 && $uploaded_images === 0) ? 1 : 0;
                            
                            $sql = "INSERT INTO ad_images (ad_id, image_path, is_primary, sort_order) VALUES (?, ?, ?, ?)";
                            $db->query($sql, [$ad_id, $image_path, $is_primary, $current_images_count + $uploaded_images]);
                            
                            $uploaded_images++;
                        }
                    }
                }
            }
            
            $success = 'تم تحديث الإعلان بنجاح وهو قيد المراجعة';
            
            // تحديث بيانات الإعلان
            $ad = array_merge($ad, $data);
            
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء تحديث الإعلان';
        }
    }
}

// جلب الفئات
$categories = $db->fetchAll("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY sort_order");
$subcategories = $db->fetchAll("SELECT * FROM categories WHERE parent_id IS NOT NULL ORDER BY parent_id, sort_order");

// جلب صور الإعلان
$images = $db->fetchAll("SELECT * FROM ad_images WHERE ad_id = ? ORDER BY is_primary DESC, sort_order", [$ad_id]);

$page_title = 'تعديل الإعلان: ' . $ad['title'];
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0 rounded-lg mt-4">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-4">
                        <i class="fas fa-edit"></i> تعديل الإعلان
                    </h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?= $success ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" enctype="multipart/form-data" id="editAdForm">
                        <!-- معلومات أساسية -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3"><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="title" name="title" 
                                   placeholder="عنوان الإعلان" maxlength="200" 
                                   value="<?= htmlspecialchars($ad['title']) ?>" required>
                            <label for="title">عنوان الإعلان *</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="description" name="description" 
                                      placeholder="وصف الإعلان" style="height: 120px" maxlength="1000" required><?= htmlspecialchars($ad['description']) ?></textarea>
                            <label for="description">وصف الإعلان *</label>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>" <?= $ad['category_id'] == $category['id'] ? 'selected' : '' ?>>
                                                <?= $category['name'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="category_id">الفئة *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="subcategory_id" name="subcategory_id">
                                        <option value="">اختر الفئة الفرعية</option>
                                    </select>
                                    <label for="subcategory_id">الفئة الفرعية</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- السعر والحالة -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-tag"></i> السعر والحالة</h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="price" name="price" 
                                           placeholder="السعر" min="0" step="0.01" 
                                           value="<?= $ad['price'] ?>">
                                    <label for="price">السعر (ريال)</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="price_type" name="price_type">
                                        <option value="fixed" <?= $ad['price_type'] === 'fixed' ? 'selected' : '' ?>>سعر ثابت</option>
                                        <option value="negotiable" <?= $ad['price_type'] === 'negotiable' ? 'selected' : '' ?>>قابل للتفاوض</option>
                                        <option value="free" <?= $ad['price_type'] === 'free' ? 'selected' : '' ?>>مجاني</option>
                                    </select>
                                    <label for="price_type">نوع السعر</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <select class="form-select" id="condition_type" name="condition_type">
                                <option value="new" <?= $ad['condition_type'] === 'new' ? 'selected' : '' ?>>جديد</option>
                                <option value="used" <?= $ad['condition_type'] === 'used' ? 'selected' : '' ?>>مستعمل</option>
                                <option value="refurbished" <?= $ad['condition_type'] === 'refurbished' ? 'selected' : '' ?>>مجدد</option>
                            </select>
                            <label for="condition_type">حالة المنتج</label>
                        </div>
                        
                        <!-- الموقع -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-map-marker-alt"></i> الموقع</h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="region" name="region" required>
                                        <option value="">اختر المنطقة</option>
                                        <?php
                                        global $regions;
                                        foreach ($regions as $region => $cities):
                                        ?>
                                            <option value="<?= $region ?>" <?= $ad['region'] === $region ? 'selected' : '' ?>>
                                                <?= $region ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="region">المنطقة *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="city" name="city" required>
                                        <option value="">اختر المدينة</option>
                                    </select>
                                    <label for="city">المدينة *</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- معلومات التواصل -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-phone"></i> معلومات التواصل</h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                           placeholder="رقم الهاتف" 
                                           value="<?= htmlspecialchars($ad['contact_phone']) ?>">
                                    <label for="contact_phone">رقم الهاتف</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           placeholder="البريد الإلكتروني" 
                                           value="<?= htmlspecialchars($ad['contact_email']) ?>">
                                    <label for="contact_email">البريد الإلكتروني</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="whatsapp" name="whatsapp" 
                                           placeholder="رقم الواتساب" 
                                           value="<?= htmlspecialchars($ad['whatsapp']) ?>">
                                    <label for="whatsapp">رقم الواتساب</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الصور الحالية -->
                        <?php if (!empty($images)): ?>
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-images"></i> الصور الحالية</h5>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <?php foreach ($images as $image): ?>
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <img src="../uploads/<?= $image['image_path'] ?>" class="card-img-top" style="height: 150px; object-fit: cover;">
                                    <div class="card-body p-2">
                                        <?php if ($image['is_primary']): ?>
                                            <span class="badge bg-primary">صورة رئيسية</span>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-danger btn-sm w-100 mt-1 delete-image" 
                                                data-image-id="<?= $image['id'] ?>">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                        
                        <!-- إضافة صور جديدة -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 mt-4"><i class="fas fa-plus"></i> إضافة صور جديدة</h5>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="images" class="form-label">اختر صور جديدة (حد أقصى 5 صور إجمالي)</label>
                            <input type="file" class="form-control" id="images" name="images[]" 
                                   accept="image/*" multiple>
                            <div class="form-text">
                                الصور المدعومة: JPG, PNG, GIF (حد أقصى 5 ميجابايت لكل صورة)
                            </div>
                        </div>
                        
                        <div id="imagePreview" class="row"></div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="my-ads.php" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات المناطق والمدن
const regions = <?= json_encode($regions) ?>;
const subcategories = <?= json_encode($subcategories) ?>;

document.addEventListener('DOMContentLoaded', function() {
    const regionSelect = document.getElementById('region');
    const citySelect = document.getElementById('city');
    const categorySelect = document.getElementById('category_id');
    const subcategorySelect = document.getElementById('subcategory_id');
    
    // تحديث المدن عند تغيير المنطقة
    regionSelect.addEventListener('change', function() {
        const selectedRegion = this.value;
        citySelect.innerHTML = '<option value="">اختر المدينة</option>';
        
        if (selectedRegion && regions[selectedRegion]) {
            regions[selectedRegion].forEach(function(city) {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                citySelect.appendChild(option);
            });
        }
    });
    
    // تحديد المدينة المحفوظة
    const savedCity = '<?= $ad['city'] ?>';
    if (savedCity) {
        regionSelect.dispatchEvent(new Event('change'));
        setTimeout(() => {
            citySelect.value = savedCity;
        }, 100);
    }
    
    // تحديث الفئات الفرعية
    categorySelect.addEventListener('change', function() {
        const selectedCategory = parseInt(this.value);
        subcategorySelect.innerHTML = '<option value="">اختر الفئة الفرعية</option>';
        
        if (selectedCategory) {
            const relatedSubcategories = subcategories.filter(sub => sub.parent_id == selectedCategory);
            relatedSubcategories.forEach(function(subcategory) {
                const option = document.createElement('option');
                option.value = subcategory.id;
                option.textContent = subcategory.name;
                subcategorySelect.appendChild(option);
            });
        }
    });
    
    // تحديد الفئة الفرعية المحفوظة
    categorySelect.dispatchEvent(new Event('change'));
    
    // حذف الصور
    document.querySelectorAll('.delete-image').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const imageId = this.getAttribute('data-image-id');
            
            if (confirm('هل تريد حذف هذه الصورة؟')) {
                // إرسال طلب حذف الصورة
                fetch('ajax/delete-image.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({image_id: imageId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.closest('.col-md-3').remove();
                    } else {
                        alert('حدث خطأ أثناء حذف الصورة');
                    }
                });
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
