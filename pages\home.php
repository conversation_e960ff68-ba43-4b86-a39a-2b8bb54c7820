<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

try {
    require_once $root_path . '/config/database.php';
    require_once $root_path . '/config/config.php';
    require_once $root_path . '/includes/functions.php';
} catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}

$page_title = 'الصفحة الرئيسية - حراجنا';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <meta name="description" content="موقع حراجنا للإعلانات المبوبة في المملكة العربية السعودية">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/simple-style.css" rel="stylesheet">
</head>
<body>

<!-- شريط التنقل -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="home.php">
            <i class="fas fa-store"></i> حراجنا
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="home.php">الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="categories.php">الفئات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.php">من نحن</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.php">اتصل بنا</a>
                </li>
            </ul>

            <ul class="navbar-nav">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= $_SESSION['full_name'] ?? 'المستخدم' ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="my-ads.php">إعلاناتي</a></li>
                            <li><a class="dropdown-item" href="favorites.php">المفضلة</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-success text-white ms-2" href="add-ad.php">
                            <i class="fas fa-plus"></i> أضف إعلان
                        </a>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">تسجيل الدخول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-success text-white ms-2" href="register.php">
                            إنشاء حساب
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>




    <!-- المحتوى الرئيسي -->
    <div class="container-fluid mt-3">
        <!-- الإعلانات العامة -->
        <?php
        $announcements = $db->fetchAll("
            SELECT * FROM announcements
            WHERE is_active = 1 AND show_on_homepage = 1
            ORDER BY created_at DESC
            LIMIT 3
        ");

        if (!empty($announcements)):
        ?>
        <div class="row mb-3">
            <div class="col-12">
                <?php foreach ($announcements as $announcement): ?>
                    <div class="alert alert-<?= $announcement['type'] ?> alert-dismissible fade show" role="alert">
                        <h6 class="alert-heading mb-2">
                            <i class="fas fa-bullhorn"></i> <?= $announcement['title'] ?>
                        </h6>
                        <p class="mb-0"><?= $announcement['content'] ?></p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Hero Section مصغر -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-info text-white">
                    <div class="card-body text-center py-3">
                        <h2 class="h4 mb-2"><i class="fas fa-store"></i> مرحباً بك في حراجنا</h2>
                        <p class="mb-3">منصة الإعلانات المبوبة الأولى في المملكة العربية السعودية</p>
                        <div class="d-flex flex-wrap justify-content-center gap-2">
                            <a href="categories.php" class="btn btn-light btn-sm">
                                <i class="fas fa-search"></i> تصفح الإعلانات
                            </a>
                            <?php if (!isset($_SESSION['user_id'])): ?>
                                <a href="register.php" class="btn btn-outline-light btn-sm">
                                    <i class="fas fa-user-plus"></i> إنشاء حساب
                                </a>
                            <?php else: ?>
                                <a href="add-ad.php" class="btn btn-outline-light btn-sm">
                                    <i class="fas fa-plus"></i> أضف إعلان
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- زر البحث -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <form action="search.php" method="GET" class="row g-2 align-items-center">
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="q" placeholder="ابحث عن إعلان..." style="border: 2px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.1); color: white;">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-light w-100" style="font-weight: 600;">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- التخطيط الجديد -->
        <div class="row">
            <!-- الفئات الرئيسية - الجانب الأيمن (تظهر تحت الإعلانات في الجوال) -->
            <div class="col-lg-3 col-md-4 order-2 order-lg-1 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-th-large"></i> الفئات الرئيسية</h5>
                    </div>
                    <div class="card-body p-2">
                        <?php
                        $categories = $db->fetchAll("
                            SELECT c.*,
                                   (SELECT COUNT(*) FROM ads a WHERE a.category_id = c.id AND a.status = 'active') as ads_count
                            FROM categories c
                            WHERE c.parent_id IS NULL AND c.is_active = 1
                            ORDER BY c.sort_order, c.name
                            LIMIT 10
                        ");

                        foreach ($categories as $index => $category):
                            $margin_top = ($index > 0) ? 'mt-2' : '';
                        ?>
                            <a href="category.php?id=<?= $category['id'] ?>"
                               class="d-block text-decoration-none <?= $margin_top ?>">
                                <div class="card border-0 bg-light hover-shadow" style="transition: all 0.3s;">
                                    <div class="card-body p-2">
                                        <div class="d-flex align-items-center">
                                            <div class="me-2">
                                                <?php if ($category['icon']): ?>
                                                    <i class="<?= $category['icon'] ?> text-primary"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-folder text-primary"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold text-dark" style="font-size: 0.85rem;">
                                                    <?= $category['name'] ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?= number_format($category['ads_count']) ?> إعلان
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        <?php endforeach; ?>

                        <div class="text-center mt-3">
                            <a href="categories.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-th"></i> جميع الفئات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي - الوسط (يظهر أولاً في الجوال) -->
            <div class="col-lg-9 col-md-8 order-1 order-lg-2">
                <!-- أحدث الإعلانات -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-clock"></i> أحدث الإعلانات</h5>
                    </div>
                        <?php
                        // جلب أحدث الإعلانات
                        $latest_ads = $db->fetchAll("
                            SELECT a.*, c.name as category_name, u.username,
                                   (SELECT image_path FROM ad_images WHERE ad_id = a.id ORDER BY is_primary DESC, id ASC LIMIT 1) as main_image
                            FROM ads a
                            LEFT JOIN categories c ON a.category_id = c.id
                            LEFT JOIN users u ON a.user_id = u.id
                            WHERE a.status = 'active'
                            ORDER BY a.created_at DESC
                            LIMIT 8
                        ");

                        if (!empty($latest_ads)):
                        ?>
                        <div class="row g-2">
                            <?php foreach ($latest_ads as $ad): ?>
                                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-2">
                                    <div class="card h-100 border-0 shadow-sm hover-card">
                                        <a href="ad-details.php?id=<?= $ad['id'] ?>" class="text-decoration-none">
                                            <div class="position-relative">
                                                <?php if ($ad['main_image']): ?>
                                                    <img src="../uploads/<?= $ad['main_image'] ?>"
                                                         class="card-img-top"
                                                         style="height: 120px; object-fit: cover;"
                                                         alt="<?= $ad['title'] ?>">
                                                <?php else: ?>
                                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                                         style="height: 120px;">
                                                        <i class="fas fa-image text-muted fa-2x"></i>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if ($ad['is_featured']): ?>
                                                    <span class="badge bg-warning position-absolute top-0 start-0 m-1">
                                                        <i class="fas fa-star"></i> مميز
                                                    </span>
                                                <?php endif; ?>
                                            </div>

                                            <div class="card-body p-2">
                                                <h6 class="card-title text-dark mb-1" style="font-size: 0.85rem; line-height: 1.2;">
                                                    <?= mb_substr($ad['title'], 0, 40) ?><?= mb_strlen($ad['title']) > 40 ? '...' : '' ?>
                                                </h6>

                                                <div class="d-flex justify-content-between align-items-center mb-1">
                                                    <span class="text-primary fw-bold" style="font-size: 0.9rem;">
                                                        <?= formatPrice($ad['price']) ?>
                                                    </span>
                                                    <small class="text-muted">
                                                        <?= $ad['category_name'] ?>
                                                    </small>
                                                </div>

                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="fas fa-map-marker-alt"></i> <?= $ad['city'] ?>
                                                    </small>
                                                    <small class="text-muted">
                                                        <?= timeAgo($ad['created_at']) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="text-center mt-3">
                            <a href="search.php" class="btn btn-outline-success">
                                <i class="fas fa-eye"></i> عرض جميع الإعلانات
                            </a>
                        </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد إعلانات حالياً</h5>
                                <a href="add-ad.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> أضف أول إعلان
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>


            </div>
        </div>

        <!-- إحصائيات الموقع في الأسفل -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white text-center">
                        <h4 class="mb-0"><i class="fas fa-chart-bar"></i> إحصائيات الموقع</h4>
                    </div>
                    <div class="card-body">
                        <?php
                        // جلب الإحصائيات الحقيقية
                        try {
                            $stats = [
                                'users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE user_type = 'user'")['count'] ?? 0,
                                'ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE status = 'active'")['count'] ?? 0,
                                'categories' => $db->fetch("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'] ?? 0,
                                'today_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE DATE(created_at) = CURDATE() AND status = 'active'")['count'] ?? 0
                            ];
                        } catch (Exception $e) {
                            $stats = ['users' => 0, 'ads' => 0, 'categories' => 0, 'today_ads' => 0];
                        }
                        ?>

                        <div class="row text-center">
                            <div class="col-lg-3 col-md-6 col-6 mb-3">
                                <div class="p-3 bg-info text-white rounded">
                                    <i class="fas fa-users fa-2x text-white mb-2"></i>
                                    <h4 class="text-white"><?= number_format($stats['users']) ?></h4>
                                    <p class="mb-0 text-white">المستخدمين</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-6 mb-3">
                                <div class="p-3 bg-info text-white rounded">
                                    <i class="fas fa-bullhorn fa-2x text-white mb-2"></i>
                                    <h4 class="text-white"><?= number_format($stats['ads']) ?></h4>
                                    <p class="mb-0 text-white">الإعلانات النشطة</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-6 mb-3">
                                <div class="p-3 bg-info text-white rounded">
                                    <i class="fas fa-th-large fa-2x text-white mb-2"></i>
                                    <h4 class="text-white"><?= number_format($stats['categories']) ?></h4>
                                    <p class="mb-0 text-white">الفئات</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-6 mb-3">
                                <div class="p-3 bg-info text-white rounded">
                                    <i class="fas fa-calendar-day fa-2x text-white mb-2"></i>
                                    <h4 class="text-white"><?= number_format($stats['today_ads']) ?></h4>
                                    <p class="mb-0 text-white">إعلانات اليوم</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



<?php include 'includes/footer.php'; ?>
