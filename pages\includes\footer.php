    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5><i class="fas fa-store"></i> حراجنا</h5>
                    <p>موقع حراجنا هو أكبر موقع للإعلانات المبوبة في المملكة العربية السعودية، يربط بين البائعين والمشترين في جميع أنحاء المملكة.</p>
                    <div class="social-links">
                        <a href="#" class="me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="home.php">الرئيسية</a></li>
                        <li><a href="categories.php">الفئات</a></li>
                        <li><a href="about.php">من نحن</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="terms.php">شروط الاستخدام</a></li>
                        <li><a href="privacy.php">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>الفئات الرئيسية</h5>
                    <ul class="list-unstyled">
                        <li><a href="category-ads.php?category=1">سيارات</a></li>
                        <li><a href="category-ads.php?category=2">عقارات</a></li>
                        <li><a href="category-ads.php?category=3">أجهزة إلكترونية</a></li>
                        <li><a href="category-ads.php?category=4">أثاث ومنزل</a></li>
                        <li><a href="category-ads.php?category=5">أزياء وموضة</a></li>
                        <li><a href="category-ads.php?category=6">وظائف</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>المدن الرئيسية</h5>
                    <ul class="list-unstyled">
                        <li><a href="city-ads.php?city=الرياض">الرياض</a></li>
                        <li><a href="city-ads.php?city=جدة">جدة</a></li>
                        <li><a href="city-ads.php?city=الدمام">الدمام</a></li>
                        <li><a href="city-ads.php?city=مكة المكرمة">مكة المكرمة</a></li>
                        <li><a href="city-ads.php?city=المدينة المنورة">المدينة المنورة</a></li>
                        <li><a href="city-ads.php?city=الطائف">الطائف</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-phone"></i> 0501234567</li>
                        <li><i class="fas fa-map-marker-alt"></i> الرياض، المملكة العربية السعودية</li>
                    </ul>
                    
                    <h6 class="mt-3">تطبيق الجوال</h6>
                    <div class="app-links">
                        <a href="#" class="d-block mb-2">
                            <img src="../assets/images/app-store.png" alt="App Store" style="height: 40px;">
                        </a>
                        <a href="#" class="d-block">
                            <img src="../assets/images/google-play.png" alt="Google Play" style="height: 40px;">
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p>&copy; <?= date('Y') ?> حراجنا. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p>تم التطوير بواسطة <a href="#" style="color: #667eea;">فريق حراجنا</a></p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-info" style="position: fixed; bottom: 20px; left: 20px; display: none; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        $(document).ready(function() {
            // Back to top button
            $(window).scroll(function() {
                if ($(this).scrollTop() > 100) {
                    $('#backToTop').fadeIn();
                } else {
                    $('#backToTop').fadeOut();
                }
            });
            
            $('#backToTop').click(function() {
                $('html, body').animate({scrollTop: 0}, 800);
                return false;
            });
            
            // Auto-hide alerts
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // Add fade-in animation to cards
            $('.ad-card, .category-card').addClass('fade-in-up');
            
            // Lazy loading for images
            $('img').each(function() {
                $(this).on('load', function() {
                    $(this).addClass('loaded');
                });
            });
            
            // Search form enhancements
            $('.search-form').on('submit', function() {
                const searchInput = $(this).find('input[name="search"]');
                if (searchInput.val().trim() === '') {
                    searchInput.focus();
                    return false;
                }
            });
            
            // Price range validation
            $('input[name="min_price"], input[name="max_price"]').on('change', function() {
                const minPrice = parseFloat($('input[name="min_price"]').val()) || 0;
                const maxPrice = parseFloat($('input[name="max_price"]').val()) || 0;
                
                if (minPrice > 0 && maxPrice > 0 && minPrice > maxPrice) {
                    alert('الحد الأدنى للسعر لا يمكن أن يكون أكبر من الحد الأقصى');
                    $(this).val('');
                }
            });
            
            // Add to favorites
            $('.add-to-favorites').on('click', function(e) {
                e.preventDefault();
                const adId = $(this).data('ad-id');
                const button = $(this);
                
                $.post('ajax/toggle-favorite.php', {ad_id: adId}, function(response) {
                    if (response.success) {
                        if (response.added) {
                            button.html('<i class="fas fa-heart"></i> مضاف للمفضلة');
                            button.removeClass('btn-outline-danger').addClass('btn-danger');
                        } else {
                            button.html('<i class="far fa-heart"></i> أضف للمفضلة');
                            button.removeClass('btn-danger').addClass('btn-outline-danger');
                        }
                    }
                }, 'json');
            });
            
            // Contact seller modal
            $('.contact-seller').on('click', function(e) {
                e.preventDefault();
                const adId = $(this).data('ad-id');
                $('#contactModal').modal('show');
                $('#contactForm input[name="ad_id"]').val(adId);
            });
            
            // Report ad
            $('.report-ad').on('click', function(e) {
                e.preventDefault();
                const adId = $(this).data('ad-id');
                $('#reportModal').modal('show');
                $('#reportForm input[name="ad_id"]').val(adId);
            });
            
            // Share ad
            $('.share-ad').on('click', function(e) {
                e.preventDefault();
                const url = window.location.href;
                const title = document.title;
                
                if (navigator.share) {
                    navigator.share({
                        title: title,
                        url: url
                    });
                } else {
                    // Fallback: copy to clipboard
                    navigator.clipboard.writeText(url).then(function() {
                        alert('تم نسخ الرابط إلى الحافظة');
                    });
                }
            });
            
            // Image gallery
            $('.ad-image img').on('click', function() {
                const src = $(this).attr('src');
                const modal = $('<div class="modal fade" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-body text-center"><img src="' + src + '" class="img-fluid"></div></div></div></div>');
                $('body').append(modal);
                modal.modal('show');
                modal.on('hidden.bs.modal', function() {
                    modal.remove();
                });
            });
            
            // Auto-complete for search
            $('input[name="search"]').on('input', function() {
                const query = $(this).val();
                if (query.length > 2) {
                    $.get('ajax/search-suggestions.php', {q: query}, function(data) {
                        // Implement autocomplete dropdown
                    }, 'json');
                }
            });
            
            // Location detection
            $('.detect-location').on('click', function(e) {
                e.preventDefault();
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        // Use position to detect city
                        // This would require a geocoding service
                        console.log('Location detected:', position.coords);
                    });
                }
            });
            
            // Form validation
            $('form').on('submit', function() {
                let isValid = true;
                
                $(this).find('input[required], select[required], textarea[required]').each(function() {
                    if ($(this).val().trim() === '') {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                return isValid;
            });
            
            // Real-time character count for textareas
            $('textarea[maxlength]').each(function() {
                const maxLength = $(this).attr('maxlength');
                const counter = $('<small class="text-muted">0/' + maxLength + '</small>');
                $(this).after(counter);
                
                $(this).on('input', function() {
                    const currentLength = $(this).val().length;
                    counter.text(currentLength + '/' + maxLength);
                    
                    if (currentLength > maxLength * 0.9) {
                        counter.removeClass('text-muted').addClass('text-warning');
                    } else {
                        counter.removeClass('text-warning').addClass('text-muted');
                    }
                });
            });
            
            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                const target = $($(this).attr('href'));
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 800);
                }
            });
            
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
            
            // Initialize popovers
            $('[data-bs-toggle="popover"]').popover();
        });
        
        // Global functions
        function formatPrice(price) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(price);
        }
        
        function showNotification(message, type = 'success') {
            const alertClass = 'alert-' + type;
            const iconClass = type === 'success' ? 'fa-check-circle' : 
                             type === 'error' ? 'fa-exclamation-circle' : 
                             type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';
            
            const alert = $(`
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas ${iconClass}"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('body').append(alert);
            
            setTimeout(function() {
                alert.fadeOut(function() {
                    alert.remove();
                });
            }, 5000);
        }
        
        function updateViewCount(adId) {
            $.post('ajax/update-view-count.php', {ad_id: adId});
        }
        
        // Performance optimization
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Lazy loading implementation
        function lazyLoad() {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        }
        
        // Initialize lazy loading if supported
        if ('IntersectionObserver' in window) {
            document.addEventListener('DOMContentLoaded', lazyLoad);
        }
    </script>
    
    <?php if (isset($extra_js)): ?>
        <?= $extra_js ?>
    <?php endif; ?>
</body>
</html>
