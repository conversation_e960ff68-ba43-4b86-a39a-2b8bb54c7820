<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isLoggedIn()) {
    header('Location: home.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        $result = $auth->login($username, $password, $remember);
        
        if ($result['success']) {
            $_SESSION['success'] = 'تم تسجيل الدخول بنجاح';
            
            // إعادة توجيه إلى الصفحة المطلوبة أو الرئيسية
            $redirect = $_GET['redirect'] ?? 'home.php';
            header('Location: ' . $redirect);
            exit();
        } else {
            $error = $result['message'];
        }
    }
}

$page_title = 'تسجيل الدخول';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0 rounded-lg mt-5">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-4">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </h3>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?= $success ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="username" name="username" 
                                   placeholder="اسم المستخدم أو البريد الإلكتروني" 
                                   value="<?= htmlspecialchars($username ?? '') ?>" required>
                            <label for="username">اسم المستخدم أو البريد الإلكتروني</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="كلمة المرور" required>
                            <label for="password">كلمة المرور</label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                تذكرني
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <a href="forgot-password.php">نسيت كلمة المرور؟</a>
                    </div>
                    <div class="small mt-2">
                        ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a>
                    </div>
                </div>
            </div>
            
            <!-- تسجيل دخول سريع للاختبار -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> للاختبار السريع</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="quickLogin('admin', 'password')">
                                <i class="fas fa-user-shield"></i> دخول كمدير
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="quickLogin('user', 'password')">
                                <i class="fas fa-user"></i> دخول كمستخدم
                            </button>
                        </div>
                    </div>
                    <small class="text-muted d-block mt-2">
                        * هذه الأزرار للاختبار فقط وستتم إزالتها في الإنتاج
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function quickLogin(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
    document.querySelector('form').submit();
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // التركيز على حقل اسم المستخدم
    document.getElementById('username').focus();
    
    // إظهار/إخفاء كلمة المرور
    const passwordInput = document.getElementById('password');
    const toggleButton = document.createElement('button');
    toggleButton.type = 'button';
    toggleButton.className = 'btn btn-outline-secondary position-absolute end-0 top-50 translate-middle-y me-2';
    toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
    toggleButton.style.border = 'none';
    toggleButton.style.background = 'transparent';
    
    passwordInput.parentElement.style.position = 'relative';
    passwordInput.parentElement.appendChild(toggleButton);
    
    toggleButton.addEventListener('click', function() {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            passwordInput.type = 'password';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        }
    });
    
    // التحقق من صحة النموذج
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            e.preventDefault();
            showNotification('جميع الحقول مطلوبة', 'error');
            return;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            return;
        }
        
        // إظهار مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
    });
    
    // حفظ اسم المستخدم في localStorage
    const usernameInput = document.getElementById('username');
    const savedUsername = localStorage.getItem('harajuna_username');
    if (savedUsername) {
        usernameInput.value = savedUsername;
    }
    
    usernameInput.addEventListener('change', function() {
        localStorage.setItem('harajuna_username', this.value);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
