<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود أعمدة الإعلانات المباعة
try {
    $sold_columns = $db->fetchAll("SHOW COLUMNS FROM ads LIKE 'is_sold'");
    $has_sold_columns = !empty($sold_columns);
} catch (Exception $e) {
    $has_sold_columns = false;
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && isset($_POST['ad_id'])) {
        $ad_id = (int)$_POST['ad_id'];
        $action = $_POST['action'];
        
        // التحقق من ملكية الإعلان
        $ad = $db->fetch("SELECT id FROM ads WHERE id = ? AND user_id = ?", [$ad_id, $_SESSION['user_id']]);
        
        if ($ad) {
            switch ($action) {
                case 'delete':
                    // حذف الصور أولاً
                    $images = $db->fetchAll("SELECT image_path FROM ad_images WHERE ad_id = ?", [$ad_id]);
                    foreach ($images as $image) {
                        deleteImage($image['image_path']);
                    }
                    
                    // حذف الإعلان
                    $db->query("DELETE FROM ads WHERE id = ?", [$ad_id]);
                    $_SESSION['success'] = 'تم حذف الإعلان بنجاح';
                    break;
                    
                case 'reactivate':
                    $db->query("UPDATE ads SET status = 'pending', expires_at = DATE_ADD(NOW(), INTERVAL 30 DAY) WHERE id = ?", [$ad_id]);
                    $_SESSION['success'] = 'تم إعادة تفعيل الإعلان وهو قيد المراجعة';
                    break;
                    
                case 'mark_sold':
                    if ($has_sold_columns) {
                        $sold_at = date('Y-m-d H:i:s');
                        $hide_sold_at = date('Y-m-d H:i:s', strtotime('+3 days'));
                        $db->query("UPDATE ads SET is_sold = 1, sold_at = ?, hide_sold_at = ? WHERE id = ?", [$sold_at, $hide_sold_at, $ad_id]);
                        $_SESSION['success'] = 'تم تحديد الإعلان كمباع. سيختفي تلقائياً بعد 3 أيام ويحذف نهائياً بعد أسبوع.';
                    } else {
                        // إذا لم تكن أعمدة الإعلانات المباعة موجودة، نغير الحالة إلى sold
                        $db->query("UPDATE ads SET status = 'sold' WHERE id = ?", [$ad_id]);
                        $_SESSION['success'] = 'تم تحديد الإعلان كمباع.';
                    }
                    break;
            }
        }
        
        header('Location: my-ads.php');
        exit();
    }
}

// جلب إعلانات المستخدم
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 10;
$offset = ($page - 1) * $items_per_page;

$where_conditions = ["user_id = ?"];
$params = [$_SESSION['user_id']];

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// جلب الإعلانات
$sql = "
    SELECT a.*, c.name as category_name,
           (SELECT image_path FROM ad_images WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_image,
           (SELECT COUNT(*) FROM favorites WHERE ad_id = a.id) as favorites_count
    FROM ads a
    JOIN categories c ON a.category_id = c.id
    WHERE $where_clause
    ORDER BY a.created_at DESC
    LIMIT $items_per_page OFFSET $offset
";

$ads = $db->fetchAll($sql, $params);

// عدد النتائج الإجمالي
$count_sql = "SELECT COUNT(*) as total FROM ads WHERE $where_clause";
$total_ads = $db->fetch($count_sql, $params)['total'];
$total_pages = ceil($total_ads / $items_per_page);

// إحصائيات سريعة
if ($has_sold_columns) {
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ?", [$_SESSION['user_id']])['count'],
        'active' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ? AND status = 'active' AND (is_sold = 0 OR is_sold IS NULL)", [$_SESSION['user_id']])['count'],
        'pending' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ? AND status = 'pending'", [$_SESSION['user_id']])['count'],
        'sold' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ? AND is_sold = 1", [$_SESSION['user_id']])['count'],
    ];
} else {
    $stats = [
        'total' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ?", [$_SESSION['user_id']])['count'],
        'active' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ? AND status = 'active'", [$_SESSION['user_id']])['count'],
        'pending' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ? AND status = 'pending'", [$_SESSION['user_id']])['count'],
        'sold' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ? AND status = 'sold'", [$_SESSION['user_id']])['count'],
    ];
}

$page_title = 'إعلاناتي';
include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bullhorn"></i> إعلاناتي</h2>
                <a href="add-ad.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة إعلان جديد
                </a>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['total']) ?></h4>
                                    <p class="mb-0">إجمالي الإعلانات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bullhorn fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['active']) ?></h4>
                                    <p class="mb-0">نشطة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['pending']) ?></h4>
                                    <p class="mb-0">قيد المراجعة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['sold']) ?></h4>
                                    <p class="mb-0">مباعة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-handshake fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلتر الحالة -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <a href="?status=all" class="btn <?= $status_filter === 'all' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                    الكل (<?= $stats['total'] ?>)
                                </a>
                                <a href="?status=active" class="btn <?= $status_filter === 'active' ? 'btn-success' : 'btn-outline-success' ?>">
                                    نشطة (<?= $stats['active'] ?>)
                                </a>
                                <a href="?status=pending" class="btn <?= $status_filter === 'pending' ? 'btn-warning' : 'btn-outline-warning' ?>">
                                    قيد المراجعة (<?= $stats['pending'] ?>)
                                </a>
                                <a href="?status=sold" class="btn <?= $status_filter === 'sold' ? 'btn-info' : 'btn-outline-info' ?>">
                                    مباعة (<?= $stats['sold'] ?>)
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <small class="text-muted">
                                عرض <?= count($ads) ?> من أصل <?= number_format($total_ads) ?> إعلان
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة الإعلانات -->
            <?php if (!empty($ads)): ?>
                <?php foreach ($ads as $ad): ?>
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <?php if ($ad['primary_image']): ?>
                                    <img src="../uploads/<?= $ad['primary_image'] ?>" 
                                         class="img-fluid rounded" 
                                         style="height: 100px; object-fit: cover; width: 100%;" 
                                         alt="<?= $ad['title'] ?>">
                                <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="height: 100px;">
                                        <i class="fas fa-image text-muted fa-2x"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-2">
                                    <a href="ad-details.php?id=<?= $ad['id'] ?>" class="text-decoration-none">
                                        <?= $ad['title'] ?>
                                    </a>
                                    <?php if ($ad['is_featured']): ?>
                                        <span class="badge bg-warning text-dark ms-2">
                                            <i class="fas fa-star"></i> مميز
                                        </span>
                                    <?php endif; ?>
                                </h5>
                                <p class="text-muted mb-2"><?= $ad['category_name'] ?></p>
                                <div class="d-flex align-items-center text-muted small">
                                    <span class="me-3">
                                        <i class="fas fa-map-marker-alt"></i> <?= $ad['city'] ?>
                                    </span>
                                    <span class="me-3">
                                        <i class="fas fa-eye"></i> <?= number_format($ad['views_count']) ?>
                                    </span>
                                    <span class="me-3">
                                        <i class="fas fa-heart"></i> <?= number_format($ad['favorites_count']) ?>
                                    </span>
                                    <span>
                                        <i class="fas fa-clock"></i> <?= timeAgo($ad['created_at']) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-2 text-center">
                                <div class="h5 text-success mb-2"><?= formatPrice($ad['price']) ?></div>
                                <?php if (($has_sold_columns && $ad['is_sold']) || (!$has_sold_columns && $ad['status'] === 'sold')): ?>
                                    <span class="badge bg-success">مباع</span>
                                    <?php if ($has_sold_columns && isset($ad['sold_at'])): ?>
                                        <br><small class="text-muted">
                                            <?= date('Y-m-d', strtotime($ad['sold_at'])) ?>
                                        </small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge bg-<?=
                                        $ad['status'] === 'active' ? 'success' :
                                        ($ad['status'] === 'pending' ? 'warning' : 'secondary')
                                    ?>">
                                        <?php
                                        $status_labels = [
                                            'active' => 'نشط',
                                            'pending' => 'قيد المراجعة',
                                            'expired' => 'منتهي الصلاحية',
                                            'rejected' => 'مرفوض'
                                        ];
                                        echo $status_labels[$ad['status']] ?? $ad['status'];
                                        ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-2">
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle w-100" 
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i> إجراءات
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="ad-details.php?id=<?= $ad['id'] ?>">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="edit-ad.php?id=<?= $ad['id'] ?>">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                        </li>
                                        <?php if ($ad['status'] === 'active' && (($has_sold_columns && !$ad['is_sold']) || (!$has_sold_columns && $ad['status'] !== 'sold'))): ?>
                                        <li>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                <input type="hidden" name="action" value="mark_sold">
                                                <button type="submit" class="dropdown-item"
                                                        onclick="return confirm('هل أنت متأكد من تحديد هذا الإعلان كمباع؟ سيختفي تلقائياً بعد 3 أيام.')">
                                                    <i class="fas fa-handshake"></i> تحديد كمباع
                                                </button>
                                            </form>
                                        </li>
                                        <?php endif; ?>
                                        <?php if (in_array($ad['status'], ['expired', 'rejected'])): ?>
                                        <li>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                <input type="hidden" name="action" value="reactivate">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-redo"></i> إعادة تفعيل
                                                </button>
                                            </form>
                                        </li>
                                        <?php endif; ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="ad_id" value="<?= $ad['id'] ?>">
                                                <input type="hidden" name="action" value="delete">
                                                <button type="submit" class="dropdown-item text-danger delete-btn" 
                                                        data-title="<?= $ad['title'] ?>">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>

                <!-- الصفحات -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="صفحات الإعلانات">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">السابق</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-4x text-muted mb-3"></i>
                    <h4>لا توجد إعلانات</h4>
                    <p class="text-muted">لم تقم بإضافة أي إعلانات بعد</p>
                    <a href="add-ad.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> أضف إعلانك الأول
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأكيد الحذف
    document.querySelectorAll('.delete-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const title = this.getAttribute('data-title');
            
            if (confirm(`هل أنت متأكد من حذف الإعلان "${title}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                this.closest('form').submit();
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
