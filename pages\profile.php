<?php
session_start();

// تعيين مسار الجذر
$root_path = dirname(__DIR__);

require_once $root_path . '/config/database.php';
require_once $root_path . '/config/config.php';
require_once $root_path . '/includes/functions.php';
require_once $root_path . '/includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$error = '';
$success = '';

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        $data = [
            'full_name' => sanitize($_POST['full_name'] ?? ''),
            'email' => sanitize($_POST['email'] ?? ''),
            'phone' => sanitize($_POST['phone'] ?? ''),
            'city' => sanitize($_POST['city'] ?? ''),
            'region' => sanitize($_POST['region'] ?? ''),
        ];
        
        // التحقق من صحة البيانات
        if (empty($data['full_name']) || empty($data['email'])) {
            $error = 'الاسم الكامل والبريد الإلكتروني مطلوبان';
        } elseif (!validateEmail($data['email'])) {
            $error = 'البريد الإلكتروني غير صحيح';
        } elseif (!empty($data['phone']) && !validatePhone($data['phone'])) {
            $error = 'رقم الهاتف غير صحيح';
        } else {
            // التحقق من عدم تكرار البريد الإلكتروني
            $existing = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$data['email'], $_SESSION['user_id']]);
            if ($existing) {
                $error = 'البريد الإلكتروني مستخدم بالفعل';
            } else {
                try {
                    $sql = "UPDATE users SET full_name = ?, email = ?, phone = ?, city = ?, region = ?, updated_at = NOW() WHERE id = ?";
                    $db->query($sql, [
                        $data['full_name'],
                        $data['email'],
                        $data['phone'],
                        $data['city'],
                        $data['region'],
                        $_SESSION['user_id']
                    ]);
                    
                    // تحديث بيانات الجلسة
                    $_SESSION['full_name'] = $data['full_name'];
                    $_SESSION['email'] = $data['email'];
                    
                    $success = 'تم تحديث الملف الشخصي بنجاح';
                } catch (Exception $e) {
                    $error = 'حدث خطأ أثناء تحديث البيانات';
                }
            }
        }
    } elseif (isset($_POST['change_password'])) {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = 'جميع حقول كلمة المرور مطلوبة';
        } elseif ($new_password !== $confirm_password) {
            $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
        } elseif (strlen($new_password) < 6) {
            $error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        } else {
            // التحقق من كلمة المرور الحالية
            $user = $db->fetch("SELECT password FROM users WHERE id = ?", [$_SESSION['user_id']]);
            if (!verifyPassword($current_password, $user['password'])) {
                $error = 'كلمة المرور الحالية غير صحيحة';
            } else {
                $hashed_password = hashPassword($new_password);
                $db->query("UPDATE users SET password = ? WHERE id = ?", [$hashed_password, $_SESSION['user_id']]);
                $success = 'تم تغيير كلمة المرور بنجاح';
            }
        }
    }
}

// جلب بيانات المستخدم
$user = $db->fetch("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);

// جلب إحصائيات المستخدم
$stats = [
    'total_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ?", [$_SESSION['user_id']])['count'],
    'active_ads' => $db->fetch("SELECT COUNT(*) as count FROM ads WHERE user_id = ? AND status = 'active'", [$_SESSION['user_id']])['count'],
    'total_views' => $db->fetch("SELECT SUM(views_count) as total FROM ads WHERE user_id = ?", [$_SESSION['user_id']])['total'] ?? 0,
    'total_favorites' => $db->fetch("SELECT COUNT(*) as count FROM favorites f JOIN ads a ON f.ad_id = a.id WHERE a.user_id = ?", [$_SESSION['user_id']])['count'],
];

$page_title = 'الملف الشخصي';
include 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <!-- الشريط الجانبي -->
        <div class="col-lg-3">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px; font-size: 32px;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5><?= $user['full_name'] ?></h5>
                    <p class="text-muted">@<?= $user['username'] ?></p>
                    <small class="text-muted">عضو منذ <?= date('Y', strtotime($user['created_at'])) ?></small>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="card shadow-sm mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar"></i> إحصائياتي</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h4 text-primary"><?= number_format($stats['total_ads']) ?></div>
                            <small class="text-muted">إجمالي الإعلانات</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 text-success"><?= number_format($stats['active_ads']) ?></div>
                            <small class="text-muted">إعلانات نشطة</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 text-info"><?= number_format($stats['total_views']) ?></div>
                            <small class="text-muted">إجمالي المشاهدات</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 text-warning"><?= number_format($stats['total_favorites']) ?></div>
                            <small class="text-muted">إعجابات</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card shadow-sm mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-link"></i> روابط سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="my-ads.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-bullhorn"></i> إعلاناتي
                        </a>
                        <a href="favorites.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-heart"></i> المفضلة
                        </a>
                        <a href="messages.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-envelope"></i> الرسائل
                        </a>
                        <a href="add-ad.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> إضافة إعلان
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-lg-9">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- تحديث الملف الشخصي -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-edit"></i> تحديث الملف الشخصي</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?= htmlspecialchars($user['full_name']) ?>" required>
                                    <label for="full_name">الاسم الكامل *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($user['email']) ?>" required>
                                    <label for="email">البريد الإلكتروني *</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?= htmlspecialchars($user['phone']) ?>">
                            <label for="phone">رقم الهاتف</label>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="region" name="region">
                                        <option value="">اختر المنطقة</option>
                                        <?php
                                        global $regions;
                                        foreach ($regions as $region => $cities):
                                        ?>
                                            <option value="<?= $region ?>" <?= $user['region'] === $region ? 'selected' : '' ?>>
                                                <?= $region ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <label for="region">المنطقة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="city" name="city">
                                        <option value="">اختر المدينة</option>
                                    </select>
                                    <label for="city">المدينة</label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- تغيير كلمة المرور -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lock"></i> تغيير كلمة المرور</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <label for="current_password">كلمة المرور الحالية *</label>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="new_password" name="new_password" 
                                           minlength="6" required>
                                    <label for="new_password">كلمة المرور الجديدة *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           minlength="6" required>
                                    <label for="confirm_password">تأكيد كلمة المرور *</label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" name="change_password" class="btn btn-warning">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات المناطق والمدن
const regions = <?= json_encode($regions) ?>;

document.addEventListener('DOMContentLoaded', function() {
    const regionSelect = document.getElementById('region');
    const citySelect = document.getElementById('city');
    
    // تحديث المدن عند تغيير المنطقة
    regionSelect.addEventListener('change', function() {
        const selectedRegion = this.value;
        citySelect.innerHTML = '<option value="">اختر المدينة</option>';
        
        if (selectedRegion && regions[selectedRegion]) {
            regions[selectedRegion].forEach(function(city) {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                citySelect.appendChild(option);
            });
        }
    });
    
    // تحديد المدينة المحفوظة
    const savedCity = '<?= $user['city'] ?>';
    if (savedCity) {
        regionSelect.dispatchEvent(new Event('change'));
        setTimeout(() => {
            citySelect.value = savedCity;
        }, 100);
    }
    
    // التحقق من تطابق كلمة المرور
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function checkPasswordMatch() {
        if (newPassword.value && confirmPassword.value) {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
    }
    
    newPassword.addEventListener('input', checkPasswordMatch);
    confirmPassword.addEventListener('input', checkPasswordMatch);
});
</script>

<?php include 'includes/footer.php'; ?>
